import {KnowledgeBaseItem} from "../stores/knowledgeBaseStore";
import {CrawledPageFlat} from "./knowledgeBaseMockData";

// Mock data for different folder contents
export const mockData: Record<string, KnowledgeBaseItem[]> = {
  // Root level items (null parent_id)
  root: [
    {
      id: 1,
      type: "article",
      source: "editor",
      parent_id: null,
      title: "Home",
      content: "# Welcome to the home page!",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-01T12:00:00Z",
      updated_at: "2023-10-01T12:00:00Z",
    },
    {
      id: 2,
      type: "folder",
      source: "editor",
      parent_id: null,
      title: "Getting Started",
      content: "",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-01T12:00:00Z",
      updated_at: "2023-10-01T12:00:00Z",
      isExpanded: false,
    },
    {
      id: 6,
      type: "folder",
      source: "editor",
      parent_id: null,
      title: "Technical Documentation",
      content: "",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-02T12:00:00Z",
      updated_at: "2023-10-02T12:00:00Z",
      isExpanded: false,
    },
    {
      id: 9,
      type: "folder",
      source: "editor",
      parent_id: null,
      title: "FAQs",
      content: "",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-03T12:00:00Z",
      updated_at: "2023-10-03T12:00:00Z",
      isExpanded: false,
    },
  ],

  // Getting Started folder contents (folder ID: 2)
  folder_2: [
    {
      id: 3,
      type: "folder",
      source: "editor",
      parent_id: 2,
      title: "Setup Guides",
      content: "",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-01T12:00:00Z",
      updated_at: "2023-10-01T12:00:00Z",
      isExpanded: false,
    },
    {
      id: 5,
      type: "article",
      source: "file",
      parent_id: 2,
      title: "Welcome Guide",
      content: "# Welcome to Knowledge Base\n\nThis is your knowledge management system...",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-01T12:00:00Z",
      updated_at: "2023-10-01T12:00:00Z",
    },
  ],

  // Setup Guides folder contents (folder ID: 3)
  folder_3: [
    {
      id: 4,
      type: "article",
      source: "editor",
      parent_id: 3,
      title: "Initial Setup",
      content: "# Initial Setup\n\nThis is a markdown article about initial setup...",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-01T12:00:00Z",
      updated_at: "2023-10-01T12:00:00Z",
    },
  ],

  // Technical Documentation folder contents (folder ID: 6)
  folder_6: [
    {
      id: 7,
      type: "folder",
      source: "editor",
      parent_id: 6,
      title: "API References",
      content: "",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "published",
      visibility: "public",
      created_at: "2023-10-02T12:00:00Z",
      updated_at: "2023-10-02T12:00:00Z",
      isExpanded: false,
    },
  ],

  // API References folder contents (folder ID: 7)
  folder_7: [
    {
      id: 8,
      type: "article",
      source: "website",
      parent_id: 7,
      title: "REST API Guide",
      content: "# REST API Guide\n\nComprehensive guide for our REST API...",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "draft",
      visibility: "private",
      created_at: "2023-10-02T12:00:00Z",
      updated_at: "2023-10-02T12:00:00Z",
    },
  ],

  // FAQs folder contents (folder ID: 9)
  folder_9: [
    {
      id: 10,
      type: "article",
      source: "editor",
      parent_id: 9,
      title: "Common Questions",
      content: "# Frequently Asked Questions\n\n## Q: How do I...?\nA: You can...",
      workspace_id: 1,
      created_by: "user123",
      updated_by: "user123",
      status: "archived",
      visibility: "public",
      created_at: "2023-10-03T12:00:00Z",
      updated_at: "2023-10-03T12:00:00Z",
    },
  ],
};

// Function to generate flat mock crawled pages based on URL (simulating backend response)
export const generateFlatMockCrawledPages = (baseUrl: string): CrawledPageFlat[] => {
  const domain = new URL(baseUrl).hostname;
  
  return [
    { url: baseUrl, title: `${domain} - Home` },
    { url: `${baseUrl}/about`, title: `About - ${domain}` },
    { url: `${baseUrl}/products`, title: `Products - ${domain}` },
    { url: `${baseUrl}/products/web-apps`, title: `Web Applications - ${domain}` },
    { url: `${baseUrl}/products/mobile-apps`, title: `Mobile Applications - ${domain}` },
    { url: `${baseUrl}/docs`, title: `Documentation - ${domain}` },
    { url: `${baseUrl}/docs/getting-started`, title: `Getting Started - ${domain}` },
    { url: `${baseUrl}/docs/getting-started/installation`, title: `Installation Guide - ${domain}` },
    { url: `${baseUrl}/docs/getting-started/configuration`, title: `Configuration Guide - ${domain}` },
    { url: `${baseUrl}/docs/getting-started/first-steps`, title: `First Steps - ${domain}` },
    { url: `${baseUrl}/docs/api-reference`, title: `API Reference - ${domain}` },
    { url: `${baseUrl}/docs/api-reference/authentication`, title: `Authentication API - ${domain}` },
    { url: `${baseUrl}/docs/api-reference/users`, title: `Users API - ${domain}` },
    { url: `${baseUrl}/docs/api-reference/data`, title: `Data API - ${domain}` },
    { url: `${baseUrl}/docs/tutorials`, title: `Tutorials - ${domain}` },
    { url: `${baseUrl}/docs/tutorials/basic-setup`, title: `Basic Setup Tutorial - ${domain}` },
    { url: `${baseUrl}/docs/tutorials/advanced-features`, title: `Advanced Features Tutorial - ${domain}` },
    { url: `${baseUrl}/docs/tutorials/deployment`, title: `Deployment Tutorial - ${domain}` },
    { url: `${baseUrl}/blog`, title: `Blog - ${domain}` },
    { url: `${baseUrl}/blog/announcements`, title: `Announcements - ${domain}` },
    { url: `${baseUrl}/blog/best-practices`, title: `Best Practices - ${domain}` },
    { url: `${baseUrl}/contact`, title: `Contact - ${domain}` }
  ];
};


// Helper function to generate mock content based on title and URL
export const generateMockContent = (title: string, url: string): string => {
  const urlPath = new URL(url).pathname;
  const segments = urlPath.split('/').filter(Boolean);
  
  if (segments.length === 0) {
    return `# Welcome to ${title}\n\nThis is the homepage content...`;
  }
  
  const lastSegment = segments[segments.length - 1];
  const sectionName = lastSegment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  if (url.includes('/docs/api-reference/')) {
    return `# ${title}\n\n${sectionName} endpoints and methods...\n\n## Example Endpoints\n\`GET /api/${lastSegment}\`\n\`POST /api/${lastSegment}\`\n\`PUT /api/${lastSegment}/:id\``;
  }
  
  if (url.includes('/docs/getting-started/')) {
    return `# ${title}\n\nStep-by-step guide for ${sectionName.toLowerCase()}...\n\n## Prerequisites\n- Basic understanding\n- Required tools\n\n## Steps\n1. First step\n2. Second step\n3. Final step`;
  }
  
  if (url.includes('/docs/tutorials/')) {
    return `# ${title}\n\nLearn ${sectionName.toLowerCase()} with this tutorial...\n\n## What You'll Learn\n- Key concepts\n- Practical examples\n- Best practices`;
  }
  
  if (url.includes('/products/')) {
    return `# ${title}\n\nExplore our ${sectionName.toLowerCase()} solutions...\n\n## Features\n- Feature 1\n- Feature 2\n- Feature 3`;
  }
  
  if (url.includes('/blog/')) {
    return `# ${title}\n\nRead our latest ${sectionName.toLowerCase()}...\n\n## Recent Posts\n- Post 1\n- Post 2\n- Post 3`;
  }
  
  return `# ${title}\n\nLearn more about ${sectionName.toLowerCase()}...`;
};