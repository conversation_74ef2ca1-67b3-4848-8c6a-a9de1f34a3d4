import { KnowledgeBaseItem } from "../stores/knowledgeBaseStore";
import {generateFlatMockCrawledPages, generateMockContent, mockData} from "./data";

// Utility function to simulate API delay
export const simulateApiDelay = (ms: number = 1800) => new Promise(resolve => setTimeout(resolve, ms));

// Unique ID generator to avoid collisions
let idCounter = 0;
const generateUniqueId = (): number => {
  return Date.now() + (++idCounter);
};

// Types for website crawling functionality
export interface CrawledPageFlat {
  url: string;
  title: string;
}

export interface CrawledPage {
  id: string;
  url: string;
  title: string;
  content: string;
  depth: number;
  parentUrl?: string;
  selected: boolean;
  children?: CrawledPage[];
}

// Helper function to get folder contents from mock data
export const fetchFolderContents = async (folderId: number | null): Promise<KnowledgeBaseItem[]> => {
  // Simulate different loading times based on folder size
  const delay = folderId === null ? 600 : Math.random() * 400 + 200;
  await simulateApiDelay(delay);
  
  // Get the appropriate mock data
  const key = folderId === null ? 'root' : `folder_${folderId}`;
  const contents = mockData[key];
  
  // Return contents or empty array for unknown folders
  return contents || [];
};

// Helper function to generate new mock item
export const createMockItem = (
  title: string,
  type: "folder" | "article",
  parentId: number | null,
  source: "editor" | "file" | "website" = "editor",
  content?: string
): KnowledgeBaseItem => {
  const baseContent = type === "article" 
    ? content || `# ${title}\n\nStart writing your article here...`
    : "";

  return {
    id: Date.now(),
    type,
    source,
    parent_id: parentId,
    title,
    content: baseContent,
    workspace_id: 1,
    created_by: "user123",
    updated_by: "user123",
    status: "draft",
    visibility: "public",
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    isExpanded: type === "folder" ? false : undefined,
  };
};

// Helper function to generate article content based on source
export const generateContentBySource = (
  title: string,
  source: "editor" | "file" | "website",
  file?: File,
  url?: string
): string => {
  switch (source) {
    case 'editor':
      return "";
    case 'file':
      return `# ${title}\n\nContent imported from ${file?.name || 'uploaded file'}.\n\n*This content was extracted from a ${file?.type || 'file'} and is read-only.*`;
    case 'website':
      return `# ${title}\n\nContent imported from ${url}.\n\n*This content was parsed from a website and is read-only.*\n\n---\n\nExample content that would be extracted from the website...`;
    default:
      return "";
  }
};


// Function to construct tree structure from flat array of URLs
const constructTreeFromFlatPages = (flatPages: CrawledPageFlat[], baseUrl: string): CrawledPage[] => {
  // Sort pages by URL depth to ensure proper parent-child relationships
  const sortedPages = [...flatPages].sort((a, b) => {
    const depthA = a.url.split('/').length;
    const depthB = b.url.split('/').length;
    return depthA - depthB;
  });

  const pageMap = new Map<string, CrawledPage>();
  const rootPages: CrawledPage[] = [];

  for (let i = 0; i < sortedPages.length; i++) {
    const flatPage = sortedPages[i];
    const urlParts = flatPage.url.split('/');
    const depth = urlParts.length - 3; // Remove protocol and domain parts
    
    // Generate mock content based on URL and title
    const content = generateMockContent(flatPage.title, flatPage.url);
    
    // Find parent URL by removing the last segment
    let parentUrl: string | undefined;
    if (depth > 0) {
      const parentUrlParts = urlParts.slice(0, -1);
      parentUrl = parentUrlParts.join('/');
      
      // Ensure parent URL exists in our data
      if (!pageMap.has(parentUrl) && parentUrl !== baseUrl.replace(/\/$/, '')) {
        parentUrl = baseUrl;
      }
    }

    const crawledPage: CrawledPage = {
      id: `page_${i + 1}`,
      url: flatPage.url,
      title: flatPage.title,
      content,
      depth: Math.max(0, depth),
      parentUrl: depth > 0 ? parentUrl : undefined,
      selected: flatPage.url === baseUrl, // Only select homepage by default
      children: []
    };

    pageMap.set(flatPage.url, crawledPage);

    // Add to parent's children or to root
    if (parentUrl && pageMap.has(parentUrl)) {
      const parent = pageMap.get(parentUrl)!;
      parent.children = parent.children || [];
      parent.children.push(crawledPage);
    } else {
      rootPages.push(crawledPage);
    }
  }

  return rootPages;
};



// Start website crawling and return pages directly (no more jobs)
export const crawlWebsite = async (url: string): Promise<CrawledPage[]> => {
  // Simulate crawling delay
  await simulateApiDelay(3000); // 3 seconds to give user feedback
  
  // Generate flat pages from backend
  const flatPages = generateFlatMockCrawledPages(url);
  
  // Convert to tree structure
  return constructTreeFromFlatPages(flatPages, url);
};

// Create articles from selected pages
export const createArticlesFromPages = async (
  pages: CrawledPage[],
  parentId: number | null
): Promise<KnowledgeBaseItem[]> => {
  await simulateApiDelay(1000);
  
  const articles: KnowledgeBaseItem[] = [];
  
  for (const page of pages) {
    if (page.selected) {
      const article = createMockItem(
        page.title,
        'article',
        parentId,
        'website',
        page.content
      );
      articles.push(article);
    }
    
    // Process children recursively
    if (page.children && page.children.length > 0) {
      const childArticles = await createArticlesFromPages(page.children, parentId);
      articles.push(...childArticles);
    }
  }
  
  return articles;
};

// Bulk create articles from prepared knowledge base items
export const bulkCreateArticlesFromCrawl = async (
  items: KnowledgeBaseItem[]
): Promise<{ folders: KnowledgeBaseItem[]; articles: KnowledgeBaseItem[] }> => {
  await simulateApiDelay(1500);
  
  const folders: KnowledgeBaseItem[] = [];
  const articles: KnowledgeBaseItem[] = [];
  
  // Simply separate folders from articles - no additional logic needed
  for (const item of items) {
    if (item.type === 'folder') {
      folders.push(item);
    } else if (item.type === 'article') {
      articles.push(item);
    }
  }
  
  return { folders, articles };
};

// Helper function to get all selected pages from a tree (including parents that have selected children)
export const getSelectedPagesForBulkCreate = (pages: CrawledPage[]): CrawledPage[] => {
  const selected: CrawledPage[] = [];
  
  const collectSelected = (pageList: CrawledPage[]): void => {
    for (const page of pageList) {
      // Include page if it's selected OR if it has selected children (needs to become a folder)
      const hasSelectedChildren = page.children && hasAnySelectedDescendants(page.children);
      
      if (page.selected || hasSelectedChildren) {
        selected.push(page);
      }
      
      // Recursively collect from children
      if (page.children) {
        collectSelected(page.children);
      }
    }
  };
  
  collectSelected(pages);
  return selected;
};

// Helper function to check if any descendants are selected
const hasAnySelectedDescendants = (pages: CrawledPage[]): boolean => {
  for (const page of pages) {
    if (page.selected) {
      return true;
    }
    if (page.children && hasAnySelectedDescendants(page.children)) {
      return true;
    }
  }
  return false;
};

// Convert crawled pages to knowledge base items with proper hierarchy
export const convertCrawledPagesToItems = (
  selectedPages: CrawledPage[], 
  baseUrl: string,
  parentId: number | null
): KnowledgeBaseItem[] => {
  const items: KnowledgeBaseItem[] = [];
  const pageToItemMap = new Map<string, number>(); // Map page URLs to item IDs
  
  // Create top-level folder for the entire website
  const websiteName = new URL(baseUrl).hostname.replace('www.', '');
  const topLevelFolderId = generateUniqueId();
  const topLevelFolder = createMockItem(
    websiteName,
    'folder',
    parentId,
    'website'
  );
  topLevelFolder.id = topLevelFolderId;
  items.push(topLevelFolder);
  pageToItemMap.set(baseUrl, topLevelFolderId);
  
  // Get all pages that need to be created (including parent folders)
  const pagesToCreate = getSelectedPagesForBulkCreate(selectedPages);
  
  // Sort by depth to ensure parents are created before children
  const sortedPages = [...pagesToCreate].sort((a, b) => a.depth - b.depth);
  
  for (const page of sortedPages) {
    // Determine parent ID
    let itemParentId = topLevelFolderId; // Default to top-level website folder
    if (page.parentUrl && pageToItemMap.has(page.parentUrl)) {
      itemParentId = pageToItemMap.get(page.parentUrl)!;
    }
    
    if (page.children && page.children.length > 0) {
      // This is a folder
      const folderId = generateUniqueId();
      const folder = createMockItem(
        page.title.replace(` - ${new URL(baseUrl).hostname}`, ''), // Clean up title
        'folder',
        itemParentId,
        'editor'
      );
      folder.id = folderId;
      items.push(folder);
      pageToItemMap.set(page.url, folderId);
    } else if (page.selected) {
      // This is a selected article
      const article = createMockItem(
        page.title.replace(` - ${new URL(baseUrl).hostname}`, ''), // Clean up title
        'article',
        itemParentId,
        'website',
        page.content
      );
      items.push(article);
    }
  }
  
  return items;
};
