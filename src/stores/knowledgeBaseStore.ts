import { create } from "zustand";
import { 
  fetchFolderContents, 
  simulateApiDelay
} from "../mocks";

// Unique ID generator to avoid collisions
let storeIdCounter = 0;
const generateUniqueStoreId = (): number => {
  return Date.now() + (++storeIdCounter);
};

export interface KnowledgeBaseItem {
  id: number;
  type: "folder" | "article";
  source: "editor" | "file" | "website";
  parent_id: number | null;
  title: string;
  content: string;
  workspace_id: number;
  created_by: string;
  updated_by: string;
  status: "draft" | "published" | "archived";
  visibility: "public" | "private";
  created_at: string;
  updated_at: string;
  // UI state for folders
  isExpanded?: boolean;
}

interface KnowledgeBaseStore {
  items: KnowledgeBaseItem[];
  loadedFolders: Set<number | null>; // Track which folders have been loaded
  isArticleEditorOpen: boolean;
  isFullscreen: boolean;
  searchQuery: string;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isFolderLoading: boolean; // New loading state for individual folder loading
  loadingMessage: string;

  // Actions
  setItems: (items: KnowledgeBaseItem[]) => void;
  addItem: (title: string, type: "folder" | "article", parentId: number | null) => Promise<void>;
  addArticle: (title: string, source: "editor" | "file" | "website", parentId: number | null, file?: File, url?: string) => Promise<number>;
  addArticleWithContent: (title: string, source: "editor" | "file" | "website", parentId: number | null, content: string, url?: string) => Promise<number>;
  bulkCreateFromCrawl: (selectedPages: any[], parentId: number | null, baseUrl: string) => Promise<void>;
  toggleFolder: (itemId: number) => Promise<void>; // Made async for lazy loading
  openArticleEditor: () => void;
  closeArticleEditor: () => void;
  toggleFullscreen: () => void;
  setSearchQuery: (query: string) => void;
  getItems: (parentId: number | null, type?: "folder" | "article") => KnowledgeBaseItem[];
  getItemsWithLoading: (parentId: number | null, type?: "folder" | "article") => Promise<KnowledgeBaseItem[]>;
  getItem: (itemId: number) => KnowledgeBaseItem | null;
  updateItem: (itemId: number, updates: Partial<KnowledgeBaseItem>) => Promise<void>;
  deleteItem: (itemId: number) => Promise<void>;
  loadItems: () => Promise<void>; // Load root level items only
  loadFolderContents: (folderId: number | null, forceReload?: boolean) => Promise<void>; // New function for lazy loading
  refreshFolderContents: (folderId: number | null) => Promise<void>; // New function to refresh folder contents
  clearFolderCache: () => void; // Clear loaded folder cache
  isFolderLoaded: (folderId: number | null) => boolean; // Check if folder is loaded
}

export const useKnowledgeBaseStore = create<KnowledgeBaseStore>((set, get) => ({
  // New structure
  items: [],
  loadedFolders: new Set<number | null>(),
  isArticleEditorOpen: false,
  isFullscreen: false,
  searchQuery: "",
  
  // Loading states
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isFolderLoading: false,
  loadingMessage: "",

  setItems: (items) => set({ items }),

  loadItems: async () => {
    set({ isLoading: true, loadingMessage: "Loading knowledge base..." });
    try {
      // Only load root level items initially
      const rootItems = await fetchFolderContents(null);
      set(() => ({ 
        items: rootItems, 
        isLoading: false, 
        loadingMessage: "",
        loadedFolders: new Set([null]) // Mark root as loaded
      }));
    } catch (error) {
      set({ isLoading: false, loadingMessage: "" });
    }
  },

  loadFolderContents: async (folderId: number | null, forceReload = false) => {
    const { loadedFolders } = get();
    
    // Don't reload if already loaded (unless forced)
    if (loadedFolders.has(folderId) && !forceReload) {
      return;
    }

    set({ isFolderLoading: true, loadingMessage: `Loading folder contents...` });
    try {
      const folderContents = await fetchFolderContents(folderId);
      
      set((state) => {
        // Remove existing items for this folder first, then add new ones
        const filteredItems = state.items.filter(item => item.parent_id !== folderId);
        
        return {
          items: [...filteredItems, ...folderContents],
          isFolderLoading: false,
          loadingMessage: "",
          loadedFolders: new Set([...state.loadedFolders, folderId])
        };
      });
    } catch (error) {
      set({ isFolderLoading: false, loadingMessage: "" });
    }
  },

  // New function to refresh folder contents
  refreshFolderContents: async (folderId: number | null) => {
    const { loadFolderContents } = get();
    await loadFolderContents(folderId, true);
  },

  // New function to clear all loaded folder cache
  clearFolderCache: () => {
    set(() => ({
      loadedFolders: new Set([null]) // Keep root as loaded
    }));
  },

  isFolderLoaded: (folderId: number | null) => {
    return get().loadedFolders.has(folderId);
  },

  addItem: async (title, type, parentId) => {
    set({ isCreating: true, loadingMessage: `Creating ${type}...` });
    try {
      await simulateApiDelay(600);
      
      const newItem: KnowledgeBaseItem = {
        id: generateUniqueStoreId(),
        type,
        source: "editor",
        parent_id: parentId,
        title,
        content: type === "article" ? `# ${title}\n\nStart writing your article here...` : "",
        workspace_id: 1,
        created_by: "user123",
        updated_by: "user123",
        status: "draft",
        visibility: "public",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        isExpanded: type === "folder" ? false : undefined,
      };

      set((state) => ({
        items: [...state.items, newItem],
        isCreating: false,
        loadingMessage: "",
        // Mark parent folder as loaded if it wasn't already (since we're adding an item to it)
        loadedFolders: new Set([...state.loadedFolders, parentId])
      }));
    } catch (error) {
      set({ isCreating: false, loadingMessage: "" });
    }
  },

  addArticle: async (title, source, parentId, file?, url?) => {
    set({ isCreating: true, loadingMessage: `Creating article...` });
    try {
      await simulateApiDelay(800);
      
      let content = "";
      
      // Generate different content based on source
      switch (source) {
        case 'editor':
          // Start with empty content for editor articles
          content = "";
          break;
        case 'file':
          content = `# ${title}\n\nContent imported from ${file?.name || 'uploaded file'}.\n\n*This content was extracted from a ${file?.type || 'file'} and is read-only.*`;
          break;
        case 'website':
          content = `# ${title}\n\nContent imported from ${url}.\n\n*This content was parsed from a website and is read-only.*\n\n---\n\nExample content that would be extracted from the website...`;
          break;
      }

      const newItem: KnowledgeBaseItem = {
        id: generateUniqueStoreId(),
        type: "article",
        source,
        parent_id: parentId,
        title,
        content,
        workspace_id: 1,
        created_by: "user123",
        updated_by: "user123",
        status: "draft",
        visibility: "public",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      set((state) => ({
        items: [...state.items, newItem],
        isCreating: false,
        loadingMessage: "",
        // Mark parent folder as loaded if it wasn't already (since we're adding an item to it)
        loadedFolders: new Set([...state.loadedFolders, parentId])
      }));
      
      return newItem.id;
    } catch (error) {
      set({ isCreating: false, loadingMessage: "" });
      throw error;
    }
  },

  addArticleWithContent: async (title, source, parentId, content) => {
    set({ isCreating: true, loadingMessage: `Creating article...` });
    try {
      await simulateApiDelay(800);

      const newItem: KnowledgeBaseItem = {
        id: generateUniqueStoreId(),
        type: "article",
        source,
        parent_id: parentId,
        title,
        content, // Use the provided content directly
        workspace_id: 1,
        created_by: "user123",
        updated_by: "user123",
        status: "draft",
        visibility: "public",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      set((state) => ({
        items: [...state.items, newItem],
        isCreating: false,
        loadingMessage: "",
        // Mark parent folder as loaded if it wasn't already (since we're adding an item to it)
        loadedFolders: new Set([...state.loadedFolders, parentId])
      }));
      
      return newItem.id;
    } catch (error) {
      set({ isCreating: false, loadingMessage: "" });
      throw error;
    }
  },

  toggleFolder: async (itemId) => {
    const folder = get().items.find(item => item.id === itemId && item.type === "folder");
    if (!folder) return;

    // If folder is being expanded and not loaded yet, load its contents
    if (!folder.isExpanded && !get().isFolderLoaded(itemId)) {
      await get().loadFolderContents(itemId);
    }

    // Toggle the folder state
    set((state) => ({
      items: state.items.map((item) =>
        item.id === itemId && item.type === "folder"
          ? { ...item, isExpanded: !item.isExpanded }
          : item
      ),
    }));
  },

  openArticleEditor: () => set({ isArticleEditorOpen: true }),
  closeArticleEditor: () => set({ isArticleEditorOpen: false }),
  toggleFullscreen: () => set((state) => ({ isFullscreen: !state.isFullscreen })),
  setSearchQuery: (query) => set({ searchQuery: query }),

  getItems: (parentId, type) => {
    const items = get().items;
    return items.filter((item) => {
      const matchesParent = item.parent_id === parentId;
      const matchesType = !type || item.type === type;
      return matchesParent && matchesType;
    });
  },

  // New function to get items with lazy loading
  getItemsWithLoading: async (parentId: number | null, type?: "folder" | "article") => {
    const { loadFolderContents, isFolderLoaded, getItems } = get();
    
    // Load folder contents if not already loaded
    if (!isFolderLoaded(parentId)) {
      await loadFolderContents(parentId);
    }
    
    return getItems(parentId, type);
  },

  getItem: (itemId) => {
    return get().items.find((item) => item.id === itemId) || null;
  },

  updateItem: async (itemId, updates) => {
    set({ isUpdating: true, loadingMessage: "Updating item..." });
    try {
      await simulateApiDelay(400);
      
      set((state) => ({
        items: state.items.map((item) =>
          item.id === itemId
            ? { ...item, ...updates, updated_at: new Date().toISOString() }
            : item
        ),
        isUpdating: false,
        loadingMessage: "",
      }));
    } catch (error) {
      set({ isUpdating: false, loadingMessage: "" });
    }
  },

  deleteItem: async (itemId) => {
    set({ isDeleting: true, loadingMessage: "Deleting item..." });
    try {
      await simulateApiDelay(500);
      
      set((state) => {
        // Recursively delete item and all its children
        const deleteItemAndChildren = (items: KnowledgeBaseItem[], targetId: number): KnowledgeBaseItem[] => {
          const filtered = items.filter((item) => item.id !== targetId);
          // Find and delete children
          const childrenToDelete = items.filter((item) => item.parent_id === targetId);
          let result = filtered;
          childrenToDelete.forEach((child) => {
            result = deleteItemAndChildren(result, child.id);
          });
          return result;
        };

        return {
          items: deleteItemAndChildren(state.items, itemId),
          isDeleting: false,
          loadingMessage: "",
        };
      });
    } catch (error) {
      set({ isDeleting: false, loadingMessage: "" });
    }
  },

  bulkCreateFromCrawl: async (selectedPages, parentId, baseUrl) => {
    set({ isCreating: true, loadingMessage: 'Creating folders and articles...' });
    try {
      // Import the functions from mocks
      const { bulkCreateArticlesFromCrawl, convertCrawledPagesToItems } = await import('../mocks');
      
      // Convert crawled pages to knowledge base items
      const itemsToCreate = convertCrawledPagesToItems(selectedPages, baseUrl, parentId);
      
      // Create the structure
      const { folders, articles } = await bulkCreateArticlesFromCrawl(itemsToCreate);
      
      // Add all items to the store
      const allItems = [...folders, ...articles];
      console.log("Bulk created items:", allItems);
      
      set((state) => ({
        items: [...state.items, ...allItems],
        isCreating: false,
        loadingMessage: "",
        // Mark parent folder as loaded if it wasn't already
        loadedFolders: new Set([...state.loadedFolders, parentId])
      }));
      
    } catch (error) {
      set({ isCreating: false, loadingMessage: "" });
      throw error;
    }
  },
}));
