// stores/useWorkspaceStore.ts
import { create } from 'zustand';
import { getWorkspaces, WorkspaceItem } from '../api/workspace';

interface WorkspaceStore {
  selectedWorkspace: WorkspaceItem | null;
  setSelectedWorkspace: (workspace: WorkspaceItem) => void;
  clearSelectedWorkspace: () => void;

  workspaces: WorkspaceItem[];
  setWorkspaces: (workspaces: WorkspaceItem[]) => void;
  refreshWorkspaces: () => Promise<void>;
}

export const useWorkspaceStore = create<WorkspaceStore>((set) => ({
  selectedWorkspace: null,
  setSelectedWorkspace: (workspace) => set({ selectedWorkspace: workspace }),
  clearSelectedWorkspace: () => set({ selectedWorkspace: null }),

  workspaces: [],
  setWorkspaces: (workspaces) => set({ workspaces }),

  refreshWorkspaces: async () => {
    try {
      const response = await getWorkspaces();
      set({ workspaces: response.data });

      // Optional: auto-select the first workspace
      if (response.data.length > 0) {
        set({ selectedWorkspace: response.data[0] });
      }
    } catch (err) {
      console.error('Failed to refresh workspaces', err);
    }
  },
}));
