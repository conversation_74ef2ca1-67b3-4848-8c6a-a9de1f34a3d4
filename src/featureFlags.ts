/**
 * Defines the available feature flags for the application.
 */
interface FeatureFlags {
  /**
   * Enables o4-mini (Preview) for all clients.
   */
  enableO4MiniPreview: boolean;
  // Add other feature flags here as needed
}

/**
 * The current state of all feature flags.
 * Set the flags to true or false to enable or disable them.
 */
export const featureFlags: FeatureFlags = {
  enableO4MiniPreview: true,
  // Initialize other flags here
};

/**
 * A helper function to check if a feature flag is enabled.
 * @param flagName The name of the feature flag to check.
 * @returns True if the flag is enabled, false otherwise.
 */
export function isFeatureEnabled(flagName: keyof FeatureFlags): boolean {
  return featureFlags[flagName] === true;
}
