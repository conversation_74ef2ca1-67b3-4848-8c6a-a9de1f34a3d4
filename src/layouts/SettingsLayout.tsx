import { Outlet, use<PERSON>ara<PERSON>, useNavigate, useLocation } from "react-router";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import {
  Settings as SettingsIcon,
  Work as WorkspaceIcon,
  Group as TeamIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Palette as ThemeIcon,
  People as PeopleIcon,
  AdminPanelSettings as RolesIcon,
  Label as LabelsIcon,
  Email as EmailIcon,
} from "@mui/icons-material";

// Define the settings navigation items
const settingsItems = [
  {
    id: 'workspace',
    label: 'Workspace',
    icon: <WorkspaceIcon />,
    path: 'workspace',
  },
  {
    id: 'users',
    label: 'Users & Permissions',
    icon: <PeopleIcon />,
    path: 'users',
  },
  {
    id: 'roles',
    label: 'Roles & Access',
    icon: <RolesIcon />,
    path: 'roles',
  },
  {
    id: 'teams',
    label: 'Teams',
    icon: <TeamIcon />,
    path: 'teams',
  },
  {
    id: 'labels',
    label: 'Labels & Segments',
    icon: <LabelsIcon />,
    path: 'labels',
  },
  {
    id: 'channels',
    label: 'Channels',
    icon: <EmailIcon />,
    path: 'channels',
  },
  {
    id: 'security',
    label: 'Security',
    icon: <SecurityIcon />,
    path: 'security',
    disabled: true, // For future implementation
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: <NotificationsIcon />,
    path: 'notifications',
    disabled: true, // For future implementation
  },
  {
    id: 'appearance',
    label: 'Appearance',
    icon: <ThemeIcon />,
    path: 'appearance',
    disabled: true, // For future implementation
  },
];

export default function SettingsLayout() {
  const { workspaceId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const handleSettingsItemClick = (item: typeof settingsItems[0]) => {
    if (item.disabled) return;
    navigate(`/app/w/${workspaceId}/settings/${item.path}`);
  };

  const isActiveItem = (item: typeof settingsItems[0]) => {
    return location.pathname.includes(`/settings/${item.path}`);
  };

  return (
    <Box
      sx={{
        display: "flex",
        height: "100vh",
        width: "100%",
        position: "relative",
        overflow: "hidden",
        margin: 0,
      }}
    >
      <Paper
        elevation={1}
        sx={{
          width: 280,
          display: "flex",
          flexDirection: "column",
          borderRadius: 0,
          zIndex: 1,
        }}
      >
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1 }}>
            <SettingsIcon sx={{ color: 'text.secondary' }} />
            <Typography variant="h5" sx={{ fontWeight: 600 }}>
              Settings
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            Workspace {workspaceId}
          </Typography>
        </Box>

        {/* Settings Navigation */}
        <Box sx={{ flex: 1, overflow: "auto" }}>
          <List dense sx={{ p: 1 }}>
            {settingsItems.map((item) => (
              <ListItem key={item.id} disablePadding>
                <ListItemButton
                  onClick={() => handleSettingsItemClick(item)}
                  disabled={item.disabled}
                  selected={isActiveItem(item)}
                  sx={{
                    borderRadius: 1,
                    mb: 0.5,
                    '&.Mui-selected': {
                      backgroundColor: 'primary.main',
                      color: 'primary.contrastText',
                      '&:hover': {
                        backgroundColor: 'primary.dark',
                      },
                      '& .MuiListItemIcon-root': {
                        color: 'inherit',
                      },
                    },
                    '&.Mui-disabled': {
                      opacity: 0.5,
                    },
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={item.label}
                    primaryTypographyProps={{
                      variant: 'body2',
                      fontWeight: isActiveItem(item) ? 600 : 400,
                    }}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Box>
      </Paper>

      {/* Main Content Area */}
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {/* Content Area */}
        <Outlet />
      </Box>
    </Box>
  );
}
