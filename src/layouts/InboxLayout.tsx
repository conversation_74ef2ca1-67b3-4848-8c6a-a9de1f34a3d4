import { Outlet } from 'react-router';
import Box from '@mui/material/Box';

export default function InboxLayout() {
  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
        margin: 0,
      }}
    >
      {/* Main Content Area */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
}
