import { Outlet } from "react-router";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import { useState, useEffect } from "react";
import CreateFolderDialog from "../pages/app/kbs/components/CreateFolderDialog";
import FolderTree from "../pages/app/kbs/components/FolderTree";
import ArticleEditor from "../pages/app/kbs/components/ArticleEditor";
import LoadingDialog from "../components/LoadingDialog";
import { useKnowledgeBaseStore } from "../stores/knowledgeBaseStore";

export default function KnowledgeBaseLayout() {
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [createFolderParentId, setCreateFolderParentId] = useState<number | null>(null);
  
  const { 
    loadItems, 
    isLoading, 
    isCreating, 
    isUpdating, 
    isDeleting, 
    loadingMessage 
  } = useKnowledgeBaseStore();

  useEffect(() => {
    loadItems();
  }, [loadItems]);

  const handleCreateFolder = (parentId: number | null) => {
    setCreateFolderParentId(parentId);
    setIsCreateFolderOpen(true);
  };

  const handleCloseCreateFolder = () => {
    setIsCreateFolderOpen(false);
    setCreateFolderParentId(null);
  };

  const showLoadingDialog = isLoading || isCreating || isUpdating || isDeleting;

  return (
      <Box
        sx={{
          display: "flex",
          height: "100vh",
          width: "100%",
          position: "relative",
          overflow: "hidden",
          margin: 0,
        }}
      >
        <Paper
          elevation={1}
          sx={{
            width: 280,
            display: "flex",
            flexDirection: "column",
            borderRadius: 0,
            zIndex: 1,
          }}
        >
          {/* Search Header */}
          <Box sx={{ p: 2, borderBottom: 1, borderColor: "divider" }}>
            <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
              Knowledge Base
            </Typography>
          </Box>

          {/* Folder Tree */}
          <Box sx={{ flex: 1, overflow: "auto", p: 1 }}>
            <FolderTree onCreateFolder={handleCreateFolder} />
          </Box>
        </Paper>

        {/* Main Content Area */}
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
          }}
        >
          {/* Content Area */}
          <Outlet />
        </Box>
        {/* Create Folder Dialog */}
        <CreateFolderDialog
          open={isCreateFolderOpen}
          onClose={handleCloseCreateFolder}
          parentId={createFolderParentId}
        />

        <ArticleEditor />

        {/* Loading Dialog */}
        <LoadingDialog 
          open={showLoadingDialog}
          message={loadingMessage || "Loading..."}
        />
      </Box>
  );
}
