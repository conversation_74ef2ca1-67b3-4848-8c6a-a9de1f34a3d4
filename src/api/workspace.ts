import { httpsCallable } from "firebase/functions";
import {functions} from "../firebase";

interface CreateWorkspaceData {
  name: string;
}
interface CreateWorkspaceResponse {
    id: string;
}

export interface WorkspaceItem {
    id: string;
    name: string;
    createdAt: string;
}


// Function to create a new workspace
export const createWorkspace = httpsCallable<CreateWorkspaceData, CreateWorkspaceResponse>(functions, "createWorkspace");

// Function to fetch all workspaces for the current user
export const getWorkspaces = httpsCallable<null, WorkspaceItem[]>(functions, "getWorkspaces");


