// Email Channels API Service
// This service provides methods to interact with the email channels backend API
// Based on the API specification provided

export interface EmailChannel {
  id: string;
  email: string;
  displayName: string;
  inboundEmail: string;
  verified: boolean;
  domainVerified: boolean;
  forwardingVerified: boolean;
  createdAt: string;
  stats?: {
    messagesReceived: number;
    messagesSent: number;
    lastActivity: string;
  };
}

export interface CreateChannelRequest {
  email: string;
  displayName: string;
}

export interface CreateChannelResponse {
  channelId: string;
  inboundEmail: string;
}

export interface VerifyOTPRequest {
  otp: string;
}

export interface DomainVerificationRequest {
  domain: string;
}

export interface DomainVerificationStatus {
  dkimVerified: boolean;
  spfVerified: boolean;
  returnPathVerified: boolean;
}

export interface DNSRecord {
  type: string;
  name: string;
  value: string;
}

class EmailChannelsAPI {
  private baseURL: string;
  private workspaceId: string;

  constructor(baseURL: string = '/api', workspaceId: string = '') {
    this.baseURL = baseURL;
    this.workspaceId = workspaceId;
  }

  // Set workspace ID for all subsequent requests
  setWorkspaceId(workspaceId: string) {
    this.workspaceId = workspaceId;
  }

  // Helper method to make authenticated requests
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'X-Workspace-ID': this.workspaceId,
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  // Get auth token from localStorage or your auth system
  private getAuthToken(): string {
    // Replace with your actual auth token retrieval logic
    return localStorage.getItem('authToken') || '';
  }

  // Email Channel Management APIs

  /**
   * Create a new email channel
   * POST /email-channels
   */
  async createEmailChannel(data: CreateChannelRequest): Promise<CreateChannelResponse> {
    return this.request<CreateChannelResponse>('/email-channels', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Get all email channels for the workspace
   * GET /email-channels
   */
  async getEmailChannels(): Promise<EmailChannel[]> {
    return this.request<EmailChannel[]>('/email-channels');
  }

  /**
   * Get a specific email channel
   * GET /email-channels/:channelId
   */
  async getEmailChannel(channelId: string): Promise<EmailChannel> {
    return this.request<EmailChannel>(`/email-channels/${channelId}`);
  }

  /**
   * Update an email channel
   * PUT /email-channels/:channelId
   */
  async updateEmailChannel(channelId: string, data: Partial<CreateChannelRequest>): Promise<EmailChannel> {
    return this.request<EmailChannel>(`/email-channels/${channelId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * Delete an email channel
   * DELETE /email-channels/:channelId
   */
  async deleteEmailChannel(channelId: string): Promise<void> {
    return this.request<void>(`/email-channels/${channelId}`, {
      method: 'DELETE',
    });
  }

  // Email Verification APIs

  /**
   * Send ownership verification OTP
   * POST /email-channels/:channelId/verify-ownership
   */
  async sendOwnershipVerification(channelId: string): Promise<void> {
    return this.request<void>(`/email-channels/${channelId}/verify-ownership`, {
      method: 'POST',
    });
  }

  /**
   * Verify ownership with OTP
   * POST /email-channels/:channelId/verify-otp
   */
  async verifyOwnershipOTP(channelId: string, data: VerifyOTPRequest): Promise<void> {
    return this.request<void>(`/email-channels/${channelId}/verify-otp`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Domain Verification APIs

  /**
   * Start domain verification and get DNS records
   * POST /email-channels/:channelId/domain-verification
   */
  async startDomainVerification(channelId: string, data: DomainVerificationRequest): Promise<DNSRecord[]> {
    return this.request<DNSRecord[]>(`/email-channels/${channelId}/domain-verification`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * Check domain verification status
   * GET /email-channels/:channelId/domain-verification/status
   */
  async getDomainVerificationStatus(channelId: string): Promise<DomainVerificationStatus> {
    return this.request<DomainVerificationStatus>(`/email-channels/${channelId}/domain-verification/status`);
  }

  // Forwarding Verification APIs

  /**
   * Test email forwarding
   * POST /email-channels/:channelId/forwarding-verification
   */
  async testEmailForwarding(channelId: string): Promise<void> {
    return this.request<void>(`/email-channels/${channelId}/forwarding-verification`, {
      method: 'POST',
    });
  }

  /**
   * Check forwarding verification status
   * GET /email-channels/:channelId/forwarding-verification/status
   */
  async getForwardingVerificationStatus(channelId: string): Promise<{ verified: boolean; lastTest?: string }> {
    return this.request<{ verified: boolean; lastTest?: string }>(`/email-channels/${channelId}/forwarding-verification/status`);
  }

  // Channel Analytics and Stats

  /**
   * Get channel statistics
   * GET /email-channels/:channelId/stats
   */
  async getChannelStats(channelId: string): Promise<EmailChannel['stats']> {
    return this.request<EmailChannel['stats']>(`/email-channels/${channelId}/stats`);
  }

  /**
   * Get aggregated workspace email stats
   * GET /email-channels/stats/summary
   */
  async getWorkspaceEmailStats(): Promise<{
    totalChannels: number;
    totalMessages: number;
    verifiedChannels: number;
    activeChannels: number;
  }> {
    return this.request('/email-channels/stats/summary');
  }
}

// Create and export a singleton instance
const emailChannelsAPI = new EmailChannelsAPI();

export default emailChannelsAPI;

// Helper hooks for React components
export const useEmailChannelsAPI = (workspaceId?: string) => {
  if (workspaceId) {
    emailChannelsAPI.setWorkspaceId(workspaceId);
  }
  
  return {
    // Channel management
    createChannel: emailChannelsAPI.createEmailChannel.bind(emailChannelsAPI),
    getChannels: emailChannelsAPI.getEmailChannels.bind(emailChannelsAPI),
    getChannel: emailChannelsAPI.getEmailChannel.bind(emailChannelsAPI),
    updateChannel: emailChannelsAPI.updateEmailChannel.bind(emailChannelsAPI),
    deleteChannel: emailChannelsAPI.deleteEmailChannel.bind(emailChannelsAPI),
    
    // Verification
    sendOwnershipVerification: emailChannelsAPI.sendOwnershipVerification.bind(emailChannelsAPI),
    verifyOTP: emailChannelsAPI.verifyOwnershipOTP.bind(emailChannelsAPI),
    startDomainVerification: emailChannelsAPI.startDomainVerification.bind(emailChannelsAPI),
    getDomainStatus: emailChannelsAPI.getDomainVerificationStatus.bind(emailChannelsAPI),
    testForwarding: emailChannelsAPI.testEmailForwarding.bind(emailChannelsAPI),
    getForwardingStatus: emailChannelsAPI.getForwardingVerificationStatus.bind(emailChannelsAPI),
    
    // Stats
    getChannelStats: emailChannelsAPI.getChannelStats.bind(emailChannelsAPI),
    getWorkspaceStats: emailChannelsAPI.getWorkspaceEmailStats.bind(emailChannelsAPI),
  };
};

// Error handling helper
export class EmailChannelsAPIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'EmailChannelsAPIError';
  }
}

// Type guards and validation helpers
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidDomain = (domain: string): boolean => {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
};

export const isValidOTP = (otp: string): boolean => {
  return /^\d{6}$/.test(otp);
};

// Constants for UI
export const VERIFICATION_STEPS = [
  'Create Email Channel',
  'Verify Email Ownership',
  'Setup Email Forwarding',
  'Domain Verification',
  'Setup Complete',
] as const;

export const CHANNEL_STATUS = {
  PENDING_VERIFICATION: 'pending_verification',
  PENDING_SETUP: 'pending_setup',
  ACTIVE: 'active',
  ERROR: 'error',
} as const;

export type ChannelStatus = typeof CHANNEL_STATUS[keyof typeof CHANNEL_STATUS];
