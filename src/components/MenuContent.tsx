import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Inbox from '@mui/icons-material/Inbox';
import Notes from '@mui/icons-material/Notes';
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded';
import { useNavigate, useParams, useLocation } from 'react-router';

const mainListItems = [
  { text: 'Inbox', icon: <Inbox />, path: 'inbox' },
  { text: 'Knowledge', icon: <Notes />, path: 'knowledge' },
];

const secondaryListItems = [
  { text: 'Settings', icon: <SettingsRoundedIcon />, path: 'settings' },
];

export default function MenuContent() {
  const navigate = useNavigate();
  const { workspaceId } = useParams();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    if (workspaceId) {
      navigate(`/app/w/${workspaceId}/${path}`);
    }
  };

  const isSelected = (path: string) => {
    return location.pathname.includes(`/${path}`);
  };

  return (
    <Stack sx={{ flexGrow: 1, p: 1, justifyContent: 'space-between' }}>
      <List dense>
        {mainListItems.map((item, index) => (
          <ListItem key={index} disablePadding sx={{ display: 'block' }}>
            <ListItemButton 
              selected={isSelected(item.path)}
              onClick={() => handleNavigation(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      <List dense>
        {secondaryListItems.map((item, index) => (
          <ListItem key={index} disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              selected={isSelected(item.path)}
              onClick={() => handleNavigation(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Stack>
  );
}