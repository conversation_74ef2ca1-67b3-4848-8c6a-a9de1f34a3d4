import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Inbox from '@mui/icons-material/Inbox';
import Notes from '@mui/icons-material/Notes';
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded';
import { useNavigate, useParams, useLocation } from 'react-router';

const mainListItems = [
  { text: 'Inbox', icon: <Inbox />, path: 'inbox' },
  { text: 'Knowledge', icon: <Notes />, path: 'knowledge' },
];

const secondaryListItems = [
  { text: 'Settings', icon: <SettingsRoundedIcon />, path: 'settings' },
];

interface MenuContentProps {
  isExpanded: boolean;
}

export default function MenuContent({ isExpanded }: MenuContentProps) {
  const navigate = useNavigate();
  const { workspaceId } = useParams();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    if (workspaceId) {
      navigate(`/app/w/${workspaceId}/${path}`);
    }
  };

  const isSelected = (path: string) => {
    return location.pathname.includes(`/${path}`);
  };

  const renderListItem = (item: typeof mainListItems[0], index: number) => {
    const listItemButton = (
      <ListItemButton
        selected={isSelected(item.path)}
        onClick={() => handleNavigation(item.path)}
        sx={{
          minHeight: 48,
          justifyContent: isExpanded ? 'initial' : 'center',
          px: 2.5,
        }}
      >
        <ListItemIcon
          sx={{
            minWidth: 0,
            mr: isExpanded ? 3 : 'auto',
            justifyContent: 'center',
          }}
        >
          {item.icon}
        </ListItemIcon>
        {isExpanded && <ListItemText primary={item.text} />}
      </ListItemButton>
    );

    return (
      <ListItem key={index} disablePadding sx={{ display: 'block' }}>
        {isExpanded ? (
          listItemButton
        ) : (
          <Tooltip title={item.text} placement="right">
            {listItemButton}
          </Tooltip>
        )}
      </ListItem>
    );
  };

  return (
    <Stack sx={{ flexGrow: 1, p: 1, justifyContent: 'space-between' }}>
      <List dense>
        {mainListItems.map((item, index) => renderListItem(item, index))}
      </List>
      <List dense>
        {secondaryListItems.map((item, index) => renderListItem(item, index))}
      </List>
    </Stack>
  );
}