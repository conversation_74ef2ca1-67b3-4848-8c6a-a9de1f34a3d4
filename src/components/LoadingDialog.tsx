import {
  Dialog,
  DialogContent,
  CircularProgress,
  Typography,
  Box
} from '@mui/material';

interface LoadingDialogProps {
  open: boolean;
  message: string;
}

export default function LoadingDialog({ open, message }: LoadingDialogProps) {
  return (
    <Dialog 
      open={open} 
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          minWidth: 300,
        }
      }}
    >
      <DialogContent>
        <Box 
          sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            gap: 2,
            py: 2 
          }}
        >
          <CircularProgress size={40} />
          <Typography variant="body1" color="text.secondary" textAlign="center">
            {message}
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
