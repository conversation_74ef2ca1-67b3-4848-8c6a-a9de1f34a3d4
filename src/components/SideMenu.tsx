import { useState } from 'react';
import { styled } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import MuiDrawer, { drawerClasses } from '@mui/material/Drawer';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import SelectContent from './SelectContent';
import MenuContent from './MenuContent';
// import CardAlert from './CardAlert';
import OptionsMenu from './OptionsMenu';

const drawerWidth = 240;
const collapsedWidth = 64;

const Drawer = styled(MuiDrawer)<{ isExpanded: boolean }>(({ theme, isExpanded }) => ({
  // Remove width from the drawer container itself to prevent layout shift
  width: 0,
  flexShrink: 0,
  boxSizing: 'border-box',
  [`& .${drawerClasses.paper}`]: {
    width: isExpanded ? drawerWidth : collapsedWidth,
    boxSizing: 'border-box',
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    position: 'fixed',
    left: 0,
    top: 0,
    height: '100vh',
    zIndex: theme.zIndex.drawer + 1,
    overflowX: 'hidden',
  },
}));

export default function SideMenu() {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleMouseEnter = () => {
    setIsExpanded(true);
  };

  const handleMouseLeave = () => {
    setIsExpanded(false);
  };

  return (
    <Drawer
      variant="permanent"
      isExpanded={isExpanded}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{
        display: { xs: 'none', md: 'block' },
        [`& .${drawerClasses.paper}`]: {
          backgroundColor: 'background.paper',
          boxShadow: isExpanded ? 3 : 1,
        },
      }}
    >
      {/* Top Section - SelectContent or collapsed icon */}
      <Box
        sx={{
          display: 'flex',
          mt: 'calc(var(--template-frame-height, 0px) + 4px)',
          p: isExpanded ? 1.5 : 1,
          justifyContent: isExpanded ? 'flex-start' : 'center',
          minHeight: 72, // Maintain consistent height
        }}
      >
        {isExpanded ? (
          <SelectContent />
        ) : (
          <Tooltip title="Workspace" placement="right">
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: 1,
                backgroundColor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
                W
              </Typography>
            </Box>
          </Tooltip>
        )}
      </Box>

      <Divider />

      {/* Menu Content */}
      <Box
        sx={{
          overflow: 'hidden',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <MenuContent isExpanded={isExpanded} />
        {/* <CardAlert /> */}
      </Box>

      {/* Bottom Section - User Profile */}
      <Stack
        direction="row"
        sx={{
          p: isExpanded ? 2 : 1,
          gap: isExpanded ? 1 : 0,
          alignItems: 'center',
          borderTop: '1px solid',
          borderColor: 'divider',
          justifyContent: isExpanded ? 'flex-start' : 'center',
          minHeight: 72, // Maintain consistent height
        }}
      >
        {isExpanded ? (
          <>
            <Avatar
              sizes="small"
              alt="Riley Carter"
              src="/static/images/avatar/7.jpg"
              sx={{ width: 36, height: 36 }}
            />
            <Box sx={{ mr: 'auto' }}>
              <Typography variant="body2" sx={{ fontWeight: 500, lineHeight: '16px' }}>
                Riley Carter
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                <EMAIL>
              </Typography>
            </Box>
            <OptionsMenu />
          </>
        ) : (
          <Tooltip title="Riley Carter" placement="right">
            <Avatar
              sizes="small"
              alt="Riley Carter"
              src="/static/images/avatar/7.jpg"
              sx={{ width: 36, height: 36, cursor: 'pointer' }}
            />
          </Tooltip>
        )}
      </Stack>
    </Drawer>
  );
}