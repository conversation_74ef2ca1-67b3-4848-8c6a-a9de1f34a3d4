import * as React from 'react';
import { ThemeProvider, createTheme, responsiveFontSizes } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import type { ThemeOptions } from '@mui/material/styles';

interface AppThemeProps {
  children: React.ReactNode;
  disableCustomTheme?: boolean;
  themeComponents?: ThemeOptions['components'];
}

export default function AppTheme({ children, disableCustomTheme, themeComponents }: AppThemeProps) {
  const theme = React.useMemo(() => {
    if (disableCustomTheme) return createTheme();

    const baseTheme = createTheme({
      cssVariables: {
        colorSchemeSelector: 'data-mui-color-scheme',
        cssVarPrefix: 'template',
      },
      palette: {
        mode: 'light',
        primary: {
          main: '#007bff', // A more vibrant blue
        },
        secondary: {
          main: '#6c757d', // A neutral grey for secondary elements
        },
        background: {
          default: '#f8f9fa', // Lighter grey for background
          paper: '#ffffff',
        },
        text: {
          primary: '#212529',
          secondary: '#495057',
        },
      },
      shape: {
        borderRadius: 8, // Slightly reduced for a more modern feel
      },
      spacing: 8, // Default MUI spacing unit
      typography: {
        fontFamily: `'Inter', 'Helvetica', 'Arial', sans-serif`,
        fontSize: 14,
        h1: {
          fontSize: '2.5rem', // 40px
          fontWeight: 700,
          lineHeight: 1.2,
        },
        h2: {
          fontSize: '2rem', // 32px
          fontWeight: 700,
          lineHeight: 1.25,
        },
        h3: {
          fontSize: '1.75rem', // 28px
          fontWeight: 600,
          lineHeight: 1.3,
        },
        h4: {
          fontSize: '1.5rem', // 24px
          fontWeight: 600,
          lineHeight: 1.35,
        },
        h5: {
          fontSize: '1.25rem', // 20px
          fontWeight: 500,
          lineHeight: 1.4,
        },
        h6: {
          fontSize: '1rem', // 16px
          fontWeight: 500,
          lineHeight: 1.45,
        },
        body1: {
          fontSize: '1rem', // 16px
        },
        body2: {
          fontSize: '0.875rem', // 14px
        },
        button: {
          textTransform: 'none',
          fontWeight: 600, // Slightly bolder button text
        },
      },
      components: {
        MuiButton: {
          defaultProps: {
            variant: 'contained',
            disableElevation: true,
            size: 'medium',
          },
          styleOverrides: {
            root: ({ theme: ownerTheme }) => ({
              borderRadius: ownerTheme.shape.borderRadius,
              padding: '8px 20px', // Adjusted padding
              textTransform: 'none',
              boxShadow: 'none',
              transition: ownerTheme.transitions.create(['background-color', 'box-shadow', 'border-color', 'color'], {
                duration: ownerTheme.transitions.duration.short,
              }),
              '&:hover': {
                boxShadow: `0 4px 12px rgba(0, 0, 0, 0.1)`, // Softer hover shadow
              },
            }),
            sizeSmall: {
              fontSize: '0.875rem',
              padding: '6px 12px',
              height: '34px',
            },
            sizeLarge: {
              fontSize: '1.125rem',
              padding: '10px 24px',
            },
          },
        },
        MuiPaper: {
          styleOverrides: {
            root: ({ theme: ownerTheme }) => ({
              borderRadius: ownerTheme.shape.borderRadius,
              boxShadow: '0 1px 3px rgba(0,0,0,0.05)', // Softer default shadow for paper
            }),
          },
        },
        MuiCard: {
          styleOverrides: {
            root: ({ theme: ownerTheme }) => ({
              borderRadius: ownerTheme.shape.borderRadius,
              boxShadow: '0 6px 18px rgba(0, 0, 0, 0.07)', // Refined card shadow
            }),
          },
        },
        MuiTypography: {
          styleOverrides: {
            // h6 was already here, now handled by typography settings above
            // If specific overrides are still needed for MuiTypography variants, they can be added here.
            // For example, if you want h6 specifically via MuiTypography to have a different color:
            // h6: {
            //   color: theme.palette.primary.main,
            // },
          },
        },
        ...themeComponents,
      },
    });

    return responsiveFontSizes(baseTheme);
  }, [disableCustomTheme, themeComponents]);

  if (disableCustomTheme) {
    return <>{children}</>;
  }

  return (
    <ThemeProvider theme={theme} disableTransitionOnChange>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}
