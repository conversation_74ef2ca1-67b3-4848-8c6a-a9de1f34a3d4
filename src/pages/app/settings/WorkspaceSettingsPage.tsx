import { useState, useEffect } from "react";
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Alert,
  Stack,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { useParams } from "react-router";

export default function WorkspaceSettingsPage() {
  const { workspaceId } = useParams();
  const [isEditing, setIsEditing] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [workspaceData, setWorkspaceData] = useState({
    name: "My Workspace",
    description: "This is a sample workspace for demonstration purposes.",
    isPublic: false,
    allowGuests: true,
    enableNotifications: true,
  });
  const [editedData, setEditedData] = useState(workspaceData);

  useEffect(() => {
    // TODO: Load actual workspace data from API
    // For now, we'll use mock data
  }, [workspaceId]);

  const handleEdit = () => {
    setEditedData(workspaceData);
    setIsEditing(true);
  };

  const handleSave = () => {
    // TODO: Save workspace data to API
    setWorkspaceData(editedData);
    setIsEditing(false);
    // Show success message
  };

  const handleCancel = () => {
    setEditedData(workspaceData);
    setIsEditing(false);
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    // TODO: Delete workspace via API
    setDeleteDialogOpen(false);
    // Redirect to workspaces list
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Workspace Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your workspace configuration and preferences.
        </Typography>
      </Box>

      {/* Basic Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" component="h2">
              Basic Information
            </Typography>
            {!isEditing && (
              <IconButton onClick={handleEdit} color="primary">
                <EditIcon />
              </IconButton>
            )}
          </Box>

          <Stack spacing={3}>
            <TextField
              label="Workspace Name"
              value={isEditing ? editedData.name : workspaceData.name}
              onChange={(e) => setEditedData({ ...editedData, name: e.target.value })}
              disabled={!isEditing}
              fullWidth
              variant={isEditing ? "outlined" : "filled"}
            />

            <TextField
              label="Description"
              value={isEditing ? editedData.description : workspaceData.description}
              onChange={(e) => setEditedData({ ...editedData, description: e.target.value })}
              disabled={!isEditing}
              fullWidth
              multiline
              rows={3}
              variant={isEditing ? "outlined" : "filled"}
            />

            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Workspace ID
              </Typography>
              <Chip
                label={workspaceId}
                variant="outlined"
                size="small"
                sx={{ fontFamily: 'monospace' }}
              />
            </Box>

            {isEditing && (
              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                >
                  Save Changes
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
              </Stack>
            )}
          </Stack>
        </CardContent>
      </Card>

      {/* Privacy & Access */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" component="h2" gutterBottom>
            Privacy & Access
          </Typography>

          <Stack spacing={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={isEditing ? editedData.isPublic : workspaceData.isPublic}
                  onChange={(e) => setEditedData({ ...editedData, isPublic: e.target.checked })}
                  disabled={!isEditing}
                />
              }
              label="Public Workspace"
            />
            <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
              Allow anyone with the link to view this workspace
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={isEditing ? editedData.allowGuests : workspaceData.allowGuests}
                  onChange={(e) => setEditedData({ ...editedData, allowGuests: e.target.checked })}
                  disabled={!isEditing}
                />
              }
              label="Allow Guest Access"
            />
            <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
              Enable guest users to collaborate without an account
            </Typography>
          </Stack>
        </CardContent>
      </Card>

      {/* Notifications */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" component="h2" gutterBottom>
            Notifications
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={isEditing ? editedData.enableNotifications : workspaceData.enableNotifications}
                onChange={(e) => setEditedData({ ...editedData, enableNotifications: e.target.checked })}
                disabled={!isEditing}
              />
            }
            label="Enable Notifications"
          />
          <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
            Receive notifications about workspace activity
          </Typography>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card sx={{ mb: 3, borderColor: 'error.main' }}>
        <CardContent>
          <Typography variant="h6" component="h2" color="error" gutterBottom>
            Danger Zone
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Irreversible and destructive actions
          </Typography>

          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
          >
            Delete Workspace
          </Button>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Workspace</DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            This action cannot be undone!
          </Alert>
          <Typography>
            Are you sure you want to delete this workspace? All data, including:
          </Typography>
          <ul>
            <li>All articles and folders</li>
            <li>Team members and permissions</li>
            <li>Settings and configurations</li>
            <li>All workspace history</li>
          </ul>
          <Typography>
            will be permanently removed.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Delete Workspace
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
