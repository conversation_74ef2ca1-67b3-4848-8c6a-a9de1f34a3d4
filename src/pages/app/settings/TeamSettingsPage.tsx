import { useState } from "react";
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Stack,
  Avatar,
  Menu,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Autocomplete,
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";

// Mock data for teams
const mockTeams = [
  {
    id: 1,
    name: "Engineering",
    description: "Software development and technical implementation",
    memberCount: 12,
    createdAt: "2024-01-15",
    color: "#2196F3",
    lead: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: null,
    },
  },
  {
    id: 2,
    name: "Product",
    description: "Product management and strategy",
    memberCount: 6,
    createdAt: "2024-01-20",
    color: "#4CAF50",
    lead: {
      name: "<PERSON>",
      email: "sarah.joh<PERSON>@example.com",
      avatar: null,
    },
  },
  {
    id: 3,
    name: "Design",
    description: "User experience and visual design",
    memberCount: 4,
    createdAt: "2024-02-01",
    color: "#FF9800",
    lead: {
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: null,
    },
  },
];

// Mock data for team members
const mockTeamMembers = [
  {
    id: 1,
    name: "John Smith",
    email: "<EMAIL>",
    role: "Team Lead",
    joinedAt: "2024-01-15",
    avatar: null,
  },
  {
    id: 2,
    name: "Jane Doe",
    email: "<EMAIL>",
    role: "Senior Developer",
    joinedAt: "2024-01-18",
    avatar: null,
  },
  {
    id: 3,
    name: "Bob Wilson",
    email: "<EMAIL>",
    role: "Developer",
    joinedAt: "2024-01-22",
    avatar: null,
  },
];

// Mock available users for adding to teams
const mockAvailableUsers = [
  { id: 4, name: "Alice Brown", email: "<EMAIL>" },
  { id: 5, name: "Charlie Davis", email: "<EMAIL>" },
  { id: 6, name: "Diana Wilson", email: "<EMAIL>" },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function TeamSettingsPage() {
  const [teams, setTeams] = useState(mockTeams);
  const [selectedTeam, setSelectedTeam] = useState<typeof mockTeams[0] | null>(null);
  const [teamMembers, setTeamMembers] = useState(mockTeamMembers);
  const [createTeamDialogOpen, setCreateTeamDialogOpen] = useState(false);
  const [editTeamDialogOpen, setEditTeamDialogOpen] = useState(false);
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMenuTeam, setSelectedMenuTeam] = useState<typeof mockTeams[0] | null>(null);
  const [tabValue, setTabValue] = useState(0);

  const [teamForm, setTeamForm] = useState({
    name: "",
    description: "",
    color: "#2196F3",
  });

  const [selectedUsers, setSelectedUsers] = useState<typeof mockAvailableUsers>([]);

  const handleCreateTeam = () => {
    const newTeam = {
      id: teams.length + 1,
      ...teamForm,
      memberCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
      lead: {
        name: "Current User",
        email: "<EMAIL>",
        avatar: null,
      },
    };
    setTeams([...teams, newTeam]);
    setCreateTeamDialogOpen(false);
    setTeamForm({ name: "", description: "", color: "#2196F3" });
  };

  const handleEditTeam = () => {
    if (selectedTeam) {
      setTeams(teams.map(team => 
        team.id === selectedTeam.id 
          ? { ...team, ...teamForm }
          : team
      ));
      setEditTeamDialogOpen(false);
      setSelectedTeam(null);
      setTeamForm({ name: "", description: "", color: "#2196F3" });
    }
  };

  const handleDeleteTeam = (teamId: number) => {
    setTeams(teams.filter(team => team.id !== teamId));
    setAnchorEl(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, team: typeof mockTeams[0]) => {
    setAnchorEl(event.currentTarget);
    setSelectedMenuTeam(team);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedMenuTeam(null);
  };

  const openEditDialog = (team: typeof mockTeams[0]) => {
    setSelectedTeam(team);
    setTeamForm({
      name: team.name,
      description: team.description,
      color: team.color,
    });
    setEditTeamDialogOpen(true);
    handleMenuClose();
  };

  const handleAddMembers = () => {
    const newMembers = selectedUsers.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: "Member",
      joinedAt: new Date().toISOString().split('T')[0],
      avatar: null,
    }));
    setTeamMembers([...teamMembers, ...newMembers]);
    setSelectedUsers([]);
    setAddMemberDialogOpen(false);
  };

  const handleRemoveMember = (memberId: number) => {
    setTeamMembers(teamMembers.filter(member => member.id !== memberId));
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Teams Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Organize your workspace members into teams for better collaboration.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateTeamDialogOpen(true)}
        >
          Create Team
        </Button>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="All Teams" />
          <Tab label="Team Details" disabled={!selectedTeam} />
        </Tabs>
      </Box>

      {/* Teams Overview Tab */}
      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Teams Overview
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Team</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Members</TableCell>
                    <TableCell>Team Lead</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {teams.map((team) => (
                    <TableRow key={team.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{ 
                              bgcolor: team.color,
                              width: 32,
                              height: 32,
                            }}
                          >
                            <GroupIcon fontSize="small" />
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight={600}>
                              {team.name}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {team.description}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={`${team.memberCount} members`}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 24, height: 24 }}>
                            <PersonIcon fontSize="small" />
                          </Avatar>
                          <Typography variant="body2">
                            {team.lead.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(team.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Button
                          size="small"
                          onClick={() => {
                            setSelectedTeam(team);
                            setTabValue(1);
                          }}
                        >
                          View Details
                        </Button>
                        <IconButton
                          onClick={(e) => handleMenuClick(e, team)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Team Details Tab */}
      <TabPanel value={tabValue} index={1}>
        {selectedTeam && (
          <Stack spacing={3}>
            {/* Team Info Card */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar
                      sx={{ 
                        bgcolor: selectedTeam.color,
                        width: 48,
                        height: 48,
                      }}
                    >
                      <GroupIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h5" component="h2">
                        {selectedTeam.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {selectedTeam.description}
                      </Typography>
                    </Box>
                  </Box>
                  <Button
                    variant="outlined"
                    startIcon={<SettingsIcon />}
                    onClick={() => openEditDialog(selectedTeam)}
                  >
                    Team Settings
                  </Button>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 4, mt: 3 }}>
                  <Box>
                    <Typography variant="h6">{selectedTeam.memberCount}</Typography>
                    <Typography variant="body2" color="text.secondary">Members</Typography>
                  </Box>
                  <Box>
                    <Typography variant="h6">{selectedTeam.lead.name}</Typography>
                    <Typography variant="body2" color="text.secondary">Team Lead</Typography>
                  </Box>
                  <Box>
                    <Typography variant="h6">
                      {new Date(selectedTeam.createdAt).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">Created</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>

            {/* Team Members Card */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Team Members ({teamMembers.length})
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => setAddMemberDialogOpen(true)}
                  >
                    Add Members
                  </Button>
                </Box>
                
                <List>
                  {teamMembers.map((member, index) => (
                    <ListItem key={member.id} divider={index < teamMembers.length - 1}>
                      <ListItemAvatar>
                        <Avatar>
                          <PersonIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={member.name}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {member.email}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                              <Chip label={member.role} size="small" />
                              <Typography variant="caption" color="text.secondary">
                                Joined {new Date(member.joinedAt).toLocaleDateString()}
                              </Typography>
                            </Box>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleRemoveMember(member.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Stack>
        )}
      </TabPanel>

      {/* Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedMenuTeam && openEditDialog(selectedMenuTeam)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Team
        </MenuItem>
        <MenuItem 
          onClick={() => selectedMenuTeam && handleDeleteTeam(selectedMenuTeam.id)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Delete Team
        </MenuItem>
      </Menu>

      {/* Create Team Dialog */}
      <Dialog open={createTeamDialogOpen} onClose={() => setCreateTeamDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Team</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Team Name"
              value={teamForm.name}
              onChange={(e) => setTeamForm({ ...teamForm, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={teamForm.description}
              onChange={(e) => setTeamForm({ ...teamForm, description: e.target.value })}
              fullWidth
              multiline
              rows={3}
            />
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Team Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#607D8B'].map((color) => (
                  <Box
                    key={color}
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      bgcolor: color,
                      cursor: 'pointer',
                      border: teamForm.color === color ? '3px solid' : '1px solid',
                      borderColor: teamForm.color === color ? 'primary.main' : 'divider',
                    }}
                    onClick={() => setTeamForm({ ...teamForm, color })}
                  />
                ))}
              </Box>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateTeamDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateTeam} 
            variant="contained"
            disabled={!teamForm.name.trim()}
          >
            Create Team
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Team Dialog */}
      <Dialog open={editTeamDialogOpen} onClose={() => setEditTeamDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Team</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Team Name"
              value={teamForm.name}
              onChange={(e) => setTeamForm({ ...teamForm, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={teamForm.description}
              onChange={(e) => setTeamForm({ ...teamForm, description: e.target.value })}
              fullWidth
              multiline
              rows={3}
            />
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Team Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {['#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0', '#607D8B'].map((color) => (
                  <Box
                    key={color}
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      bgcolor: color,
                      cursor: 'pointer',
                      border: teamForm.color === color ? '3px solid' : '1px solid',
                      borderColor: teamForm.color === color ? 'primary.main' : 'divider',
                    }}
                    onClick={() => setTeamForm({ ...teamForm, color })}
                  />
                ))}
              </Box>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditTeamDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleEditTeam} 
            variant="contained"
            disabled={!teamForm.name.trim()}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Members Dialog */}
      <Dialog open={addMemberDialogOpen} onClose={() => setAddMemberDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Team Members</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <Autocomplete
              multiple
              options={mockAvailableUsers}
              getOptionLabel={(option) => `${option.name} (${option.email})`}
              value={selectedUsers}
              onChange={(_, newValue) => setSelectedUsers(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Users"
                  placeholder="Search and select users to add"
                />
              )}
            />
            {selectedUsers.length > 0 && (
              <Alert severity="info">
                {selectedUsers.length} user(s) will be added to the team.
              </Alert>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddMemberDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleAddMembers} 
            variant="contained"
            disabled={selectedUsers.length === 0}
          >
            Add Members
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
