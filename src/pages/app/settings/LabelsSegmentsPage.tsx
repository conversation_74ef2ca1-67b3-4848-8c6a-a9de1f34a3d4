import { useState } from "react";
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Stack,
  Menu,
  MenuItem,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  Alert,
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Label as LabelIcon,
  Segment as SegmentIcon,
  FilterList as FilterIcon,
} from "@mui/icons-material";

// Mock data for labels
const mockLabels = [
  {
    id: 1,
    name: "Priority",
    color: "#F44336",
    description: "High priority conversations",
    conversationCount: 25,
    type: "conversation",
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    name: "Support",
    color: "#2196F3",
    description: "Customer support requests",
    conversationCount: 45,
    type: "conversation",
    createdAt: "2024-01-18",
  },
  {
    id: 3,
    name: "VIP Customer",
    color: "#FF9800",
    description: "VIP customer contacts",
    conversationCount: 12,
    type: "contact",
    createdAt: "2024-01-20",
  },
  {
    id: 4,
    name: "Sales Lead",
    color: "#4CAF50",
    description: "Potential sales opportunities",
    conversationCount: 18,
    type: "contact",
    createdAt: "2024-01-22",
  },
];

// Mock data for segments
const mockSegments = [
  {
    id: 1,
    name: "Active Customers",
    description: "Customers who have been active in the last 30 days",
    contactCount: 150,
    criteria: [
      { field: "last_activity", operator: "within", value: "30 days" },
      { field: "status", operator: "equals", value: "active" },
    ],
    createdAt: "2024-01-15",
  },
  {
    id: 2,
    name: "High Value Contacts",
    description: "Contacts with high engagement scores",
    contactCount: 75,
    criteria: [
      { field: "engagement_score", operator: "greater_than", value: "80" },
    ],
    createdAt: "2024-01-20",
  },
  {
    id: 3,
    name: "New Leads",
    description: "Contacts created in the last 7 days",
    contactCount: 23,
    criteria: [
      { field: "created_at", operator: "within", value: "7 days" },
    ],
    createdAt: "2024-02-01",
  },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

export default function LabelsSegmentsPage() {
  const [labels, setLabels] = useState(mockLabels);
  const [segments, setSegments] = useState(mockSegments);
  const [tabValue, setTabValue] = useState(0);
  const [createLabelDialogOpen, setCreateLabelDialogOpen] = useState(false);
  const [createSegmentDialogOpen, setCreateSegmentDialogOpen] = useState(false);
  const [editLabelDialogOpen, setEditLabelDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMenuLabel, setSelectedMenuLabel] = useState<typeof mockLabels[0] | null>(null);
  const [selectedLabel, setSelectedLabel] = useState<typeof mockLabels[0] | null>(null);

  const [labelForm, setLabelForm] = useState({
    name: "",
    description: "",
    color: "#2196F3",
    type: "conversation" as "conversation" | "contact",
  });

  const [segmentForm, setSegmentForm] = useState({
    name: "",
    description: "",
    criteria: [{ field: "", operator: "", value: "" }],
  });

  const handleCreateLabel = () => {
    const newLabel = {
      id: labels.length + 1,
      ...labelForm,
      conversationCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
    };
    setLabels([...labels, newLabel]);
    setCreateLabelDialogOpen(false);
    setLabelForm({ name: "", description: "", color: "#2196F3", type: "conversation" });
  };

  const handleEditLabel = () => {
    if (selectedLabel) {
      setLabels(labels.map(label => 
        label.id === selectedLabel.id 
          ? { ...label, ...labelForm }
          : label
      ));
      setEditLabelDialogOpen(false);
      setSelectedLabel(null);
      setLabelForm({ name: "", description: "", color: "#2196F3", type: "conversation" });
    }
  };

  const handleDeleteLabel = (labelId: number) => {
    setLabels(labels.filter(label => label.id !== labelId));
    setAnchorEl(null);
  };

  const handleCreateSegment = () => {
    const newSegment = {
      id: segments.length + 1,
      ...segmentForm,
      contactCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
    };
    setSegments([...segments, newSegment]);
    setCreateSegmentDialogOpen(false);
    setSegmentForm({
      name: "",
      description: "",
      criteria: [{ field: "", operator: "", value: "" }],
    });
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, label: typeof mockLabels[0]) => {
    setAnchorEl(event.currentTarget);
    setSelectedMenuLabel(label);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedMenuLabel(null);
  };

  const openEditDialog = (label: typeof mockLabels[0]) => {
    setSelectedLabel(label);
    setLabelForm({
      name: label.name,
      description: label.description,
      color: label.color,
      type: label.type as "conversation" | "contact",
    });
    setEditLabelDialogOpen(true);
    handleMenuClose();
  };

  const addCriterion = () => {
    setSegmentForm({
      ...segmentForm,
      criteria: [...segmentForm.criteria, { field: "", operator: "", value: "" }],
    });
  };

  const updateCriterion = (index: number, field: string, value: string) => {
    const newCriteria = [...segmentForm.criteria];
    newCriteria[index] = { ...newCriteria[index], [field]: value };
    setSegmentForm({ ...segmentForm, criteria: newCriteria });
  };

  const removeCriterion = (index: number) => {
    setSegmentForm({
      ...segmentForm,
      criteria: segmentForm.criteria.filter((_, i) => i !== index),
    });
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Labels & Segments
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Organize and categorize your conversations and contacts for better management.
        </Typography>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Labels" />
          <Tab label="Segments" />
        </Tabs>
      </Box>

      {/* Labels Tab */}
      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">
            Conversation & Contact Labels
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateLabelDialogOpen(true)}
          >
            Create Label
          </Button>
        </Box>

        <Card>
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Label</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Usage</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {labels.map((label) => (
                    <TableRow key={label.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box
                            sx={{
                              width: 16,
                              height: 16,
                              borderRadius: '50%',
                              bgcolor: label.color,
                            }}
                          />
                          <Typography variant="body2" fontWeight={600}>
                            {label.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={label.type}
                          size="small"
                          variant="outlined"
                          icon={label.type === 'conversation' ? <LabelIcon /> : <SegmentIcon />}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {label.description}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {label.conversationCount} items
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(label.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          onClick={(e) => handleMenuClick(e, label)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Segments Tab */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">
            Contact Segments
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateSegmentDialogOpen(true)}
          >
            Create Segment
          </Button>
        </Box>

        <Card>
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Segment Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Contacts</TableCell>
                    <TableCell>Criteria</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {segments.map((segment) => (
                    <TableRow key={segment.id} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <FilterIcon color="action" fontSize="small" />
                          <Typography variant="body2" fontWeight={600}>
                            {segment.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {segment.description}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={`${segment.contactCount} contacts`}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {segment.criteria.length} condition(s)
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(segment.createdAt).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <IconButton>
                          <EditIcon />
                        </IconButton>
                        <IconButton color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedMenuLabel && openEditDialog(selectedMenuLabel)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Label
        </MenuItem>
        <MenuItem 
          onClick={() => selectedMenuLabel && handleDeleteLabel(selectedMenuLabel.id)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Delete Label
        </MenuItem>
      </Menu>

      {/* Create Label Dialog */}
      <Dialog open={createLabelDialogOpen} onClose={() => setCreateLabelDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Label</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Label Name"
              value={labelForm.name}
              onChange={(e) => setLabelForm({ ...labelForm, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={labelForm.description}
              onChange={(e) => setLabelForm({ ...labelForm, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            <FormControl fullWidth>
              <InputLabel>Label Type</InputLabel>
              <Select
                value={labelForm.type}
                label="Label Type"
                onChange={(e) => setLabelForm({ ...labelForm, type: e.target.value as "conversation" | "contact" })}
              >
                <MenuItem value="conversation">Conversation</MenuItem>
                <MenuItem value="contact">Contact</MenuItem>
              </Select>
            </FormControl>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Label Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {['#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800', '#FF5722'].map((color) => (
                  <Box
                    key={color}
                    sx={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      bgcolor: color,
                      cursor: 'pointer',
                      border: labelForm.color === color ? '3px solid' : '1px solid',
                      borderColor: labelForm.color === color ? 'primary.main' : 'divider',
                    }}
                    onClick={() => setLabelForm({ ...labelForm, color })}
                  />
                ))}
              </Box>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateLabelDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateLabel} 
            variant="contained"
            disabled={!labelForm.name.trim()}
          >
            Create Label
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Label Dialog */}
      <Dialog open={editLabelDialogOpen} onClose={() => setEditLabelDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Label</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Label Name"
              value={labelForm.name}
              onChange={(e) => setLabelForm({ ...labelForm, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={labelForm.description}
              onChange={(e) => setLabelForm({ ...labelForm, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            <FormControl fullWidth>
              <InputLabel>Label Type</InputLabel>
              <Select
                value={labelForm.type}
                label="Label Type"
                onChange={(e) => setLabelForm({ ...labelForm, type: e.target.value as "conversation" | "contact" })}
              >
                <MenuItem value="conversation">Conversation</MenuItem>
                <MenuItem value="contact">Contact</MenuItem>
              </Select>
            </FormControl>
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Label Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {['#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5', '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800', '#FF5722'].map((color) => (
                  <Box
                    key={color}
                    sx={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      bgcolor: color,
                      cursor: 'pointer',
                      border: labelForm.color === color ? '3px solid' : '1px solid',
                      borderColor: labelForm.color === color ? 'primary.main' : 'divider',
                    }}
                    onClick={() => setLabelForm({ ...labelForm, color })}
                  />
                ))}
              </Box>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditLabelDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleEditLabel} 
            variant="contained"
            disabled={!labelForm.name.trim()}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Segment Dialog */}
      <Dialog open={createSegmentDialogOpen} onClose={() => setCreateSegmentDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Segment</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Segment Name"
              value={segmentForm.name}
              onChange={(e) => setSegmentForm({ ...segmentForm, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={segmentForm.description}
              onChange={(e) => setSegmentForm({ ...segmentForm, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Segment Criteria
              </Typography>
              <Alert severity="info" sx={{ mb: 2 }}>
                Define conditions that contacts must meet to be included in this segment.
              </Alert>
              
              {segmentForm.criteria.map((criterion, index) => (
                <Box key={index} sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
                  <FormControl sx={{ flex: 1 }}>
                    <InputLabel>Field</InputLabel>
                    <Select
                      value={criterion.field}
                      label="Field"
                      onChange={(e) => updateCriterion(index, 'field', e.target.value)}
                    >
                      <MenuItem value="last_activity">Last Activity</MenuItem>
                      <MenuItem value="created_at">Created Date</MenuItem>
                      <MenuItem value="engagement_score">Engagement Score</MenuItem>
                      <MenuItem value="status">Status</MenuItem>
                      <MenuItem value="tags">Tags</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl sx={{ flex: 1 }}>
                    <InputLabel>Operator</InputLabel>
                    <Select
                      value={criterion.operator}
                      label="Operator"
                      onChange={(e) => updateCriterion(index, 'operator', e.target.value)}
                    >
                      <MenuItem value="equals">Equals</MenuItem>
                      <MenuItem value="not_equals">Not Equals</MenuItem>
                      <MenuItem value="greater_than">Greater Than</MenuItem>
                      <MenuItem value="less_than">Less Than</MenuItem>
                      <MenuItem value="within">Within</MenuItem>
                      <MenuItem value="contains">Contains</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    label="Value"
                    value={criterion.value}
                    onChange={(e) => updateCriterion(index, 'value', e.target.value)}
                    sx={{ flex: 1 }}
                  />
                  
                  {segmentForm.criteria.length > 1 && (
                    <IconButton onClick={() => removeCriterion(index)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  )}
                </Box>
              ))}
              
              <Button
                startIcon={<AddIcon />}
                onClick={addCriterion}
                variant="outlined"
                size="small"
              >
                Add Criterion
              </Button>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateSegmentDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateSegment} 
            variant="contained"
            disabled={!segmentForm.name.trim() || segmentForm.criteria.some(c => !c.field || !c.operator || !c.value)}
          >
            Create Segment
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
