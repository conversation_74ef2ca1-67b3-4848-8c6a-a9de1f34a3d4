import { useState } from "react";
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Stack,
  Tabs,
  Tab,
} from "@mui/material";
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  PersonAdd as InviteIcon,
} from "@mui/icons-material";

// Mock data for account users (AccountUser model)
const mockAccountUsers = [
  {
    id: 1,
    userId: 101,
    accountId: 1,
    user: {
      id: 101,
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/avatars/john.jpg",
    },
    role: "Owner",
    status: "active",
    availability: "online",
    lastSeen: "2 hours ago",
    joinedAt: "2024-01-15",
    teams: ["Engineering", "Product"],
    customPermissions: [], // Additional permissions beyond role
  },
  {
    id: 2,
    userId: 102,
    accountId: 1,
    user: {
      id: 102,
      name: "<PERSON>",
      email: "<EMAIL>",
      avatar: "/avatars/sarah.jpg",
    },
    role: "Administrator",
    status: "active",
    availability: "online",
    lastSeen: "30 minutes ago",
    joinedAt: "2024-02-01",
    teams: ["Marketing"],
    customPermissions: ["manage_reports"],
  },
  {
    id: 3,
    userId: 103,
    accountId: 1,
    user: {
      id: 103,
      name: "Mike Wilson",
      email: "<EMAIL>",
      avatar: "/avatars/mike.jpg",
    },
    role: "Agent",
    status: "active",
    availability: "offline",
    lastSeen: "1 day ago",
    joinedAt: "2024-02-15",
    teams: ["Support"],
    customPermissions: [],
  },
];

const defaultRoles = [
  { value: "Owner", label: "Owner", description: "Full access to all features" },
  { value: "Administrator", label: "Administrator", description: "Manage users and most settings" },
  { value: "Agent", label: "Agent", description: "Handle conversations and contacts" },
  { value: "Viewer", label: "Viewer", description: "Read-only access" },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`users-tabpanel-${index}`}
      aria-labelledby={`users-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

export default function UsersPermissionsPage() {
  const [users, setUsers] = useState(mockAccountUsers);
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  
  // Invite form state
  const [inviteForm, setInviteForm] = useState({
    email: "",
    role: "Agent",
    teams: [] as string[],
  });

  // Edit user form state
  const [editForm, setEditForm] = useState({
    role: "",
    teams: [] as string[],
    status: "",
  });

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, user: any) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const handleEditUser = () => {
    if (selectedUser) {
      setEditForm({
        role: selectedUser.role,
        teams: selectedUser.teams,
        status: selectedUser.status,
      });
      setEditUserDialogOpen(true);
    }
    handleMenuClose();
  };

  const handleDeleteUser = () => {
    if (selectedUser) {
      setUsers(users.filter(u => u.id !== selectedUser.id));
    }
    handleMenuClose();
  };

  const handleInviteUser = () => {
    // TODO: API call to invite user
    console.log("Inviting user:", inviteForm);
    setInviteDialogOpen(false);
    setInviteForm({ email: "", role: "Agent", teams: [] });
  };

  const handleUpdateUser = () => {
    if (selectedUser) {
      setUsers(users.map(u => 
        u.id === selectedUser.id 
          ? { ...u, ...editForm }
          : u
      ));
    }
    setEditUserDialogOpen(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "success";
      case "offline": return "default";
      case "inactive": return "error";
      default: return "default";
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Users & Permissions
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage workspace members, roles, and access permissions.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<InviteIcon />}
          onClick={() => setInviteDialogOpen(true)}
        >
          Invite User
        </Button>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label={`Users (${users.length})`} />
          <Tab label="Pending Invitations" />
          <Tab label="User Activity" />
        </Tabs>
      </Box>

      {/* Users Tab */}
      <TabPanel value={activeTab} index={0}>
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Teams</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Seen</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((accountUser) => (
                  <TableRow key={accountUser.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar src={accountUser.user.avatar} sx={{ width: 40, height: 40 }}>
                          {accountUser.user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight={600}>
                            {accountUser.user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {accountUser.user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={accountUser.role}
                        size="small"
                        color={accountUser.role === "Owner" ? "primary" : "default"}
                        variant={accountUser.role === "Owner" ? "filled" : "outlined"}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {accountUser.teams.map((team) => (
                          <Chip
                            key={team}
                            label={team}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={accountUser.status}
                        size="small"
                        color={getStatusColor(accountUser.status) as any}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {accountUser.lastSeen}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        onClick={(e) => handleMenuOpen(e, accountUser)}
                        disabled={accountUser.role === "Owner" && accountUser.id === 1} // Prevent owner from being modified
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      </TabPanel>

      {/* Pending Invitations Tab */}
      <TabPanel value={activeTab} index={1}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Pending Invitations
            </Typography>
            <Typography variant="body2" color="text.secondary">
              No pending invitations at this time.
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>

      {/* User Activity Tab */}
      <TabPanel value={activeTab} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              User Activity
            </Typography>
            <Typography variant="body2" color="text.secondary">
              User activity logs and session information will be displayed here.
            </Typography>
          </CardContent>
        </Card>
      </TabPanel>

      {/* User Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEditUser}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit User</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => console.log("Resend invitation")}>
          <ListItemIcon>
            <EmailIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Resend Invitation</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleDeleteUser} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
          </ListItemIcon>
          <ListItemText>Remove User</ListItemText>
        </MenuItem>
      </Menu>

      {/* Invite User Dialog */}
      <Dialog open={inviteDialogOpen} onClose={() => setInviteDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Invite New User</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Email Address"
              type="email"
              value={inviteForm.email}
              onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
              fullWidth
              required
            />
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={inviteForm.role}
                onChange={(e) => setInviteForm({ ...inviteForm, role: e.target.value })}
                label="Role"
              >
                {defaultRoles.map((role) => (
                  <MenuItem key={role.value} value={role.value}>
                    <Box>
                      <Typography variant="body2">{role.label}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {role.description}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInviteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleInviteUser} variant="contained">
            Send Invitation
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={editUserDialogOpen} onClose={() => setEditUserDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit User Permissions</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={editForm.role}
                onChange={(e) => setEditForm({ ...editForm, role: e.target.value })}
                label="Role"
              >
                {defaultRoles.map((role) => (
                  <MenuItem key={role.value} value={role.value}>
                    {role.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={editForm.status}
                onChange={(e) => setEditForm({ ...editForm, status: e.target.value })}
                label="Status"
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditUserDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateUser} variant="contained">
            Update User
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
