import { useEffect } from "react";
import { useNavigate, useParams } from "react-router";

export default function SettingsIndexPage() {
  const navigate = useNavigate();
  const { workspaceId } = useParams();

  useEffect(() => {
    // Redirect to workspace settings by default
    if (workspaceId) {
      navigate(`/app/w/${workspaceId}/settings/workspace`, { replace: true });
    }
  }, [navigate, workspaceId]);

  return null; // This component just redirects
}
