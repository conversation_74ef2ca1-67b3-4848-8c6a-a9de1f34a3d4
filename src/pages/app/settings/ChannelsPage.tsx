import { useState } from "react";
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Stack,
  <PERSON>per,
  Step,
  StepLabel,
  StepContent,
  Alert,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import {
  Add as AddIcon,
  Email as EmailIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  Send as SendIcon,
} from "@mui/icons-material";

// Mock data for email channels
const mockEmailChannels = [
  {
    id: "ch_1",
    email: "<EMAIL>",
    displayName: "Customer Support",
    inboundEmail: "<EMAIL>",
    verified: true,
    domainVerified: true,
    forwardingVerified: true,
    createdAt: "2024-01-15T10:30:00Z",
    stats: {
      messagesReceived: 1247,
      messagesSent: 892,
      lastActivity: "2024-06-17T08:15:00Z",
    },
  },
  {
    id: "ch_2",
    email: "<EMAIL>",
    displayName: "Sales Inquiries",
    inboundEmail: "<EMAIL>",
    verified: true,
    domainVerified: false,
    forwardingVerified: true,
    createdAt: "2024-02-01T14:20:00Z",
    stats: {
      messagesReceived: 567,
      messagesSent: 423,
      lastActivity: "2024-06-16T16:45:00Z",
    },
  },
];

// Verification steps for email setup (for reference)
// const verificationSteps = [
//   "Create Email Channel",
//   "Verify Email Ownership",
//   "Domain Verification", 
//   "Forwarding Setup",
//   "Test Configuration",
// ];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

export default function ChannelsPage() {
  const [emailChannels, setEmailChannels] = useState(mockEmailChannels);
  const [tabValue, setTabValue] = useState(0);
  const [createChannelDialogOpen, setCreateChannelDialogOpen] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState<typeof mockEmailChannels[0] | null>(null);
  const [setupDialogOpen, setSetupDialogOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [verificationLoading, setVerificationLoading] = useState(false);

  // Form states
  const [channelForm, setChannelForm] = useState({
    email: "",
    displayName: "",
  });

  const [verificationForm, setVerificationForm] = useState({
    otp: "",
    domain: "",
    testEmail: "",
  });

  // Mock verification states
  const [verificationData, setVerificationData] = useState({
    channelId: "",
    inboundEmail: "",
    otpSent: false,
    domainRecords: [] as Array<{type: string, name: string, value: string}>,
    verificationStatus: {
      dkimVerified: false,
      spfVerified: false,
      returnPathVerified: false,
    },
  });

  const handleCreateChannel = async () => {
    setVerificationLoading(true);
    
    // Simulate API call to POST /email-channels
    setTimeout(() => {
      const newChannel = {
        id: `ch_${emailChannels.length + 1}`,
        ...channelForm,
        inboundEmail: `ch_${emailChannels.length + 1}_${Math.random().toString(36).substring(7)}@inbound.yourapp.com`,
        verified: false,
        domainVerified: false,
        forwardingVerified: false,
        createdAt: new Date().toISOString(),
        stats: {
          messagesReceived: 0,
          messagesSent: 0,
          lastActivity: new Date().toISOString(),
        },
      };

      setVerificationData({
        ...verificationData,
        channelId: newChannel.id,
        inboundEmail: newChannel.inboundEmail,
      });

      setEmailChannels([...emailChannels, newChannel]);
      setSelectedChannel(newChannel);
      setCreateChannelDialogOpen(false);
      setSetupDialogOpen(true);
      setActiveStep(1);
      setVerificationLoading(false);
    }, 2000);
  };

  const handleSendOTP = async () => {
    setVerificationLoading(true);
    
    // Simulate API call to POST /email-channels/:channelId/verify-ownership
    setTimeout(() => {
      setVerificationData({
        ...verificationData,
        otpSent: true,
      });
      setVerificationLoading(false);
    }, 1500);
  };

  const handleVerifyOTP = async () => {
    setVerificationLoading(true);
    
    // Simulate API call to POST /email-channels/:channelId/verify-otp
    setTimeout(() => {
      if (selectedChannel) {
        const updatedChannels = emailChannels.map(ch => 
          ch.id === selectedChannel.id ? { ...ch, verified: true } : ch
        );
        setEmailChannels(updatedChannels);
        setSelectedChannel({ ...selectedChannel, verified: true });
      }
      setActiveStep(2); // Move to forwarding setup step
      setVerificationLoading(false);
    }, 1500);
  };

  const handleDomainVerification = async () => {
    setVerificationLoading(true);
    
    // Simulate API call to POST /email-channels/:channelId/domain-verification
    setTimeout(() => {
      setVerificationData({
        ...verificationData,
        domainRecords: [
          { type: "DKIM", name: `k1._domainkey.${verificationForm.domain}`, value: "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQ..." },
          { type: "SPF", name: verificationForm.domain, value: "v=spf1 include:spf.yourdomain.com ~all" },
          { type: "Return-Path", name: `pm-bounces.${verificationForm.domain}`, value: "CNAME pm-bounces.postmarkapp.com" },
        ],
      });
      setActiveStep(3);
      setVerificationLoading(false);
    }, 2000);
  };

  const handleCheckDomainStatus = async () => {
    setVerificationLoading(true);
    
    // Simulate API call to GET /email-channels/:channelId/domain-verification/status
    setTimeout(() => {
      setVerificationData({
        ...verificationData,
        verificationStatus: {
          dkimVerified: true,
          spfVerified: true,
          returnPathVerified: true,
        },
      });
      
      if (selectedChannel) {
        const updatedChannels = emailChannels.map(ch => 
          ch.id === selectedChannel.id ? { ...ch, domainVerified: true } : ch
        );
        setEmailChannels(updatedChannels);
        setSelectedChannel({ ...selectedChannel, domainVerified: true });
      }
      setActiveStep(4); // Move to completion step
      setVerificationLoading(false);
    }, 2000);
  };

  const handleCompleteSetup = () => {
    setSetupDialogOpen(false);
    setActiveStep(0);
  };

  const handleTestForwarding = async () => {
    setVerificationLoading(true);
    
    // Simulate API call to POST /email-channels/:channelId/forwarding-verification
    setTimeout(() => {
      if (selectedChannel) {
        const updatedChannels = emailChannels.map(ch => 
          ch.id === selectedChannel.id ? { ...ch, forwardingVerified: true } : ch
        );
        setEmailChannels(updatedChannels);
        setSelectedChannel({ ...selectedChannel, forwardingVerified: true });
      }
      setActiveStep(3); // Move to domain verification step
      setVerificationLoading(false);
    }, 3000);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getChannelStatus = (channel: typeof mockEmailChannels[0]) => {
    if (channel.verified && channel.domainVerified && channel.forwardingVerified) {
      return { label: "Active", color: "success" as const };
    } else if (channel.verified) {
      return { label: "Pending Setup", color: "warning" as const };
    } else {
      return { label: "Verification Needed", color: "error" as const };
    }
  };

  const resetForms = () => {
    setChannelForm({ email: "", displayName: "" });
    setVerificationForm({ otp: "", domain: "", testEmail: "" });
    setActiveStep(0);
    setSelectedChannel(null);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Channels Configuration
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure and manage your communication channels for customer interactions.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            resetForms();
            setCreateChannelDialogOpen(true);
          }}
        >
          Add Channel
        </Button>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <EmailIcon />
                Email Channels
              </Box>
            } 
          />
          <Tab label="Channel Analytics" />
        </Tabs>
      </Box>

      {/* Email Channels Tab */}
      <TabPanel value={tabValue} index={0}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Email Channels ({emailChannels.length})
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Channel</TableCell>
                    <TableCell>Inbound Address</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Verification</TableCell>
                    <TableCell>Activity</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {emailChannels.map((channel) => {
                    const status = getChannelStatus(channel);
                    return (
                      <TableRow key={channel.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <EmailIcon color="primary" />
                            <Box>
                              <Typography variant="body2" fontWeight={600}>
                                {channel.displayName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {channel.email}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                              {channel.inboundEmail}
                            </Typography>
                            <IconButton 
                              size="small" 
                              onClick={() => copyToClipboard(channel.inboundEmail)}
                            >
                              <CopyIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={status.label}
                            size="small"
                            color={status.color}
                            variant={status.color === "success" ? "filled" : "outlined"}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Chip
                              icon={channel.verified ? <CheckCircleIcon /> : <WarningIcon />}
                              label="Email"
                              size="small"
                              color={channel.verified ? "success" : "default"}
                              variant="outlined"
                            />
                            <Chip
                              icon={channel.forwardingVerified ? <CheckCircleIcon /> : <WarningIcon />}
                              label="Forwarding"
                              size="small"
                              color={channel.forwardingVerified ? "success" : "default"}
                              variant="outlined"
                            />
                            <Chip
                              icon={channel.domainVerified ? <CheckCircleIcon /> : <WarningIcon />}
                              label="Domain"
                              size="small"
                              color={channel.domainVerified ? "success" : "default"}
                              variant="outlined"
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {channel.stats.messagesReceived} received
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Last: {new Date(channel.stats.lastActivity).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            onClick={() => {
                              setSelectedChannel(channel);
                              setSetupDialogOpen(true);
                            }}
                          >
                            <SettingsIcon />
                          </IconButton>
                          <IconButton color="error">
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Analytics Tab */}
      <TabPanel value={tabValue} index={1}>
        <Stack spacing={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Channel Performance
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analytics and performance metrics will be available here.
              </Typography>
            </CardContent>
          </Card>
        </Stack>
      </TabPanel>

      {/* Create Channel Dialog */}
      <Dialog open={createChannelDialogOpen} onClose={() => setCreateChannelDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create Email Channel</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <Alert severity="info">
              Create a new email channel to start receiving customer messages. You'll need to verify ownership and configure forwarding.
            </Alert>
            <TextField
              label="Email Address"
              value={channelForm.email}
              onChange={(e) => setChannelForm({ ...channelForm, email: e.target.value })}
              fullWidth
              required
              placeholder="<EMAIL>"
              helperText="The email address where customers will send messages"
            />
            <TextField
              label="Display Name"
              value={channelForm.displayName}
              onChange={(e) => setChannelForm({ ...channelForm, displayName: e.target.value })}
              fullWidth
              required
              placeholder="Customer Support"
              helperText="A friendly name to identify this channel"
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateChannelDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateChannel} 
            variant="contained"
            disabled={!channelForm.email.trim() || !channelForm.displayName.trim() || verificationLoading}
          >
            {verificationLoading ? "Creating..." : "Create & Setup"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Setup/Verification Dialog */}
      <Dialog 
        open={setupDialogOpen} 
        onClose={() => setSetupDialogOpen(false)} 
        maxWidth="md" 
        fullWidth
        PaperProps={{ sx: { minHeight: '600px' } }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <EmailIcon />
            Email Channel Setup
            {selectedChannel && (
              <Chip label={selectedChannel.displayName} size="small" />
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: Create Channel */}
            <Step>
              <StepLabel>Create Email Channel</StepLabel>
              <StepContent>
                <Typography>
                  Channel created successfully! Your inbound email address is:
                </Typography>
                <Paper sx={{ p: 2, mt: 2, bgcolor: 'grey.50' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {verificationData.inboundEmail}
                    </Typography>
                    <IconButton 
                      size="small" 
                      onClick={() => copyToClipboard(verificationData.inboundEmail)}
                    >
                      <CopyIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Paper>
                <Box sx={{ mb: 2, mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(1)}
                  >
                    Continue
                  </Button>
                </Box>
              </StepContent>
            </Step>

            {/* Step 2: Email Ownership Verification */}
            <Step>
              <StepLabel>Verify Email Ownership</StepLabel>
              <StepContent>
                <Typography gutterBottom>
                  We need to verify that you own the email address <strong>{selectedChannel?.email}</strong>.
                </Typography>
                
                {!verificationData.otpSent ? (
                  <Box sx={{ mt: 2 }}>
                    <Button
                      variant="contained"
                      onClick={handleSendOTP}
                      disabled={verificationLoading}
                      startIcon={<SendIcon />}
                    >
                      {verificationLoading ? "Sending..." : "Send Verification Code"}
                    </Button>
                  </Box>
                ) : (
                  <Stack spacing={2} sx={{ mt: 2 }}>
                    <Alert severity="success">
                      Verification code sent to {selectedChannel?.email}
                    </Alert>
                    <TextField
                      label="Verification Code"
                      value={verificationForm.otp}
                      onChange={(e) => setVerificationForm({ ...verificationForm, otp: e.target.value })}
                      placeholder="Enter 6-digit code"
                      inputProps={{ maxLength: 6 }}
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        onClick={handleVerifyOTP}
                        disabled={verificationForm.otp.length !== 6 || verificationLoading}
                      >
                        {verificationLoading ? "Verifying..." : "Verify Code"}
                      </Button>
                      <Button
                        variant="outlined"
                        onClick={handleSendOTP}
                        disabled={verificationLoading}
                      >
                        Resend Code
                      </Button>
                    </Box>
                  </Stack>
                )}
                
                {verificationLoading && <LinearProgress sx={{ mt: 2 }} />}
              </StepContent>
            </Step>

            {/* Step 3: Email Forwarding Setup */}
            <Step>
              <StepLabel>Setup Email Forwarding</StepLabel>
              <StepContent>
                <Typography gutterBottom>
                  Configure email forwarding from your address to our inbound email to start receiving messages.
                </Typography>
                
                <Stack spacing={2} sx={{ mt: 2 }}>
                  <Alert severity="info">
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>Setup Instructions:</Typography>
                      <Typography variant="body2">
                        1. Log into your email provider's admin panel<br/>
                        2. Set up email forwarding from <strong>{selectedChannel?.email}</strong><br/>
                        3. Forward all emails to: <strong>{verificationData.inboundEmail}</strong>
                      </Typography>
                    </Box>
                  </Alert>
                  
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Forward emails to:
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                        {verificationData.inboundEmail}
                      </Typography>
                      <IconButton 
                        size="small" 
                        onClick={() => copyToClipboard(verificationData.inboundEmail)}
                      >
                        <CopyIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Paper>
                  
                  <Button
                    variant="contained"
                    onClick={handleTestForwarding}
                    disabled={verificationLoading}
                    startIcon={<SendIcon />}
                  >
                    {verificationLoading ? "Testing Forwarding..." : "Test Email Forwarding"}
                  </Button>
                  
                  {verificationLoading && (
                    <Box>
                      <LinearProgress sx={{ mb: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        Sending test email and waiting for delivery confirmation...
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </StepContent>
            </Step>

            {/* Step 4: Domain Verification */}
            <Step>
              <StepLabel>Domain Verification</StepLabel>
              <StepContent>
                <Typography gutterBottom>
                  Configure DNS records to enable domain verification and improve email deliverability.
                </Typography>
                
                {verificationData.domainRecords.length === 0 ? (
                  <Stack spacing={2} sx={{ mt: 2 }}>
                    <TextField
                      label="Domain"
                      value={verificationForm.domain}
                      onChange={(e) => setVerificationForm({ ...verificationForm, domain: e.target.value })}
                      placeholder="yourcompany.com"
                      helperText="Enter your email domain (without www)"
                    />
                    <Button
                      variant="contained"
                      onClick={handleDomainVerification}
                      disabled={!verificationForm.domain.trim() || verificationLoading}
                      startIcon={<SecurityIcon />}
                    >
                      {verificationLoading ? "Generating..." : "Generate DNS Records"}
                    </Button>
                  </Stack>
                ) : (
                  <Stack spacing={2} sx={{ mt: 2 }}>
                    <Alert severity="info">
                      Add these DNS records to your domain configuration:
                    </Alert>
                    
                    {verificationData.domainRecords.map((record, index) => (
                      <Accordion key={index}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle2">
                            {record.type} Record
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Stack spacing={1}>
                            <Box>
                              <Typography variant="caption" color="text.secondary">Name:</Typography>
                              <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                                {record.name}
                              </Typography>
                            </Box>
                            <Box>
                              <Typography variant="caption" color="text.secondary">Value:</Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all', flex: 1 }}>
                                  {record.value}
                                </Typography>
                                <IconButton 
                                  size="small" 
                                  onClick={() => copyToClipboard(record.value)}
                                >
                                  <CopyIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </Box>
                          </Stack>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                    
                    <Alert severity="warning">
                      DNS changes can take up to 24 hours to propagate. Click "Check Status" once you've added the records.
                    </Alert>
                    
                    <Button
                      variant="contained"
                      onClick={handleCheckDomainStatus}
                      disabled={verificationLoading}
                      startIcon={<RefreshIcon />}
                    >
                      {verificationLoading ? "Checking..." : "Check Domain Status"}
                    </Button>
                    
                    {Object.values(verificationData.verificationStatus).some(v => v) && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>Verification Status:</Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              {verificationData.verificationStatus.dkimVerified ? 
                                <CheckCircleIcon color="success" /> : 
                                <WarningIcon color="warning" />
                              }
                            </ListItemIcon>
                            <ListItemText 
                              primary="DKIM" 
                              secondary={verificationData.verificationStatus.dkimVerified ? "Verified" : "Pending"}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              {verificationData.verificationStatus.spfVerified ? 
                                <CheckCircleIcon color="success" /> : 
                                <WarningIcon color="warning" />
                              }
                            </ListItemIcon>
                            <ListItemText 
                              primary="SPF" 
                              secondary={verificationData.verificationStatus.spfVerified ? "Verified" : "Pending"}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              {verificationData.verificationStatus.returnPathVerified ? 
                                <CheckCircleIcon color="success" /> : 
                                <WarningIcon color="warning" />
                              }
                            </ListItemIcon>
                            <ListItemText 
                              primary="Return Path" 
                              secondary={verificationData.verificationStatus.returnPathVerified ? "Verified" : "Pending"}
                            />
                          </ListItem>
                        </List>
                      </Box>
                    )}
                  </Stack>
                )}
                
                {verificationLoading && <LinearProgress sx={{ mt: 2 }} />}
              </StepContent>
            </Step>

            {/* Step 5: Complete */}
            <Step>
              <StepLabel>Setup Complete</StepLabel>
              <StepContent>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Box>
                    <Typography variant="subtitle2">Email channel setup completed!</Typography>
                    <Typography variant="body2">
                      Your email channel is now active and ready to receive customer messages.
                    </Typography>
                  </Box>
                </Alert>
                
                <Button
                  variant="contained"
                  onClick={handleCompleteSetup}
                >
                  Finish Setup
                </Button>
              </StepContent>
            </Step>
          </Stepper>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSetupDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
