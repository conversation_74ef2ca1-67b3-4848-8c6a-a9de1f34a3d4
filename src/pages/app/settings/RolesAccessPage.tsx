import { useState } from "react";
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Stack,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Alert,
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";

// Default roles with their permissions
const defaultRoles = [
  {
    id: 1,
    name: "Owner",
    description: "Full access to all features and settings",
    isDefault: true,
    userCount: 1,
    permissions: [
      "manage_workspace",
      "manage_users",
      "manage_roles",
      "manage_conversations",
      "manage_contacts",
      "manage_knowledge_base",
      "view_reports",
      "manage_integrations",
      "manage_billing"
    ]
  },
  {
    id: 2,
    name: "Administrator",
    description: "Manage users and most workspace settings",
    isDefault: true,
    userCount: 2,
    permissions: [
      "manage_users",
      "manage_conversations",
      "manage_contacts",
      "manage_knowledge_base",
      "view_reports",
      "manage_integrations"
    ]
  },
  {
    id: 3,
    name: "Agent",
    description: "Handle conversations and manage contacts",
    isDefault: true,
    userCount: 5,
    permissions: [
      "manage_conversations",
      "manage_contacts",
      "view_knowledge_base"
    ]
  },
  {
    id: 4,
    name: "Viewer",
    description: "Read-only access to conversations and reports",
    isDefault: true,
    userCount: 2,
    permissions: [
      "view_conversations",
      "view_contacts",
      "view_knowledge_base",
      "view_reports"
    ]
  }
];

// Available permissions
const availablePermissions = [
  { key: "manage_workspace", label: "Manage Workspace", description: "Edit workspace settings and configuration" },
  { key: "manage_users", label: "Manage Users", description: "Invite, edit, and remove users" },
  { key: "manage_roles", label: "Manage Roles", description: "Create and edit custom roles" },
  { key: "manage_conversations", label: "Manage Conversations", description: "Handle customer conversations" },
  { key: "view_conversations", label: "View Conversations", description: "Read-only access to conversations" },
  { key: "manage_contacts", label: "Manage Contacts", description: "Add, edit, and delete contacts" },
  { key: "view_contacts", label: "View Contacts", description: "Read-only access to contacts" },
  { key: "manage_knowledge_base", label: "Manage Knowledge Base", description: "Create and edit articles" },
  { key: "view_knowledge_base", label: "View Knowledge Base", description: "Read-only access to knowledge base" },
  { key: "view_reports", label: "View Reports", description: "Access analytics and reports" },
  { key: "manage_integrations", label: "Manage Integrations", description: "Configure third-party integrations" },
  { key: "manage_billing", label: "Manage Billing", description: "Access billing and subscription settings" },
];

export default function RolesAccessPage() {
  const [roles, setRoles] = useState(defaultRoles);
  const [createRoleDialogOpen, setCreateRoleDialogOpen] = useState(false);
  const [editRoleDialogOpen, setEditRoleDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<any>(null);
  
  // Form state for creating/editing roles
  const [roleForm, setRoleForm] = useState({
    name: "",
    description: "",
    permissions: [] as string[],
  });

  const handleCreateRole = () => {
    const newRole = {
      id: Date.now(),
      name: roleForm.name,
      description: roleForm.description,
      isDefault: false,
      userCount: 0,
      permissions: roleForm.permissions,
    };
    setRoles([...roles, newRole]);
    setCreateRoleDialogOpen(false);
    setRoleForm({ name: "", description: "", permissions: [] });
  };

  const handleEditRole = (role: any) => {
    setSelectedRole(role);
    setRoleForm({
      name: role.name,
      description: role.description,
      permissions: role.permissions,
    });
    setEditRoleDialogOpen(true);
  };

  const handleUpdateRole = () => {
    if (selectedRole) {
      setRoles(roles.map(r => 
        r.id === selectedRole.id 
          ? { ...r, ...roleForm }
          : r
      ));
    }
    setEditRoleDialogOpen(false);
    setSelectedRole(null);
    setRoleForm({ name: "", description: "", permissions: [] });
  };

  const handleDeleteRole = (roleId: number) => {
    setRoles(roles.filter(r => r.id !== roleId));
  };

  const handlePermissionToggle = (permission: string) => {
    const newPermissions = roleForm.permissions.includes(permission)
      ? roleForm.permissions.filter(p => p !== permission)
      : [...roleForm.permissions, permission];
    setRoleForm({ ...roleForm, permissions: newPermissions });
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Roles & Access Control
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage user roles and permissions for your workspace.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateRoleDialogOpen(true)}
        >
          Create Custom Role
        </Button>
      </Box>

      {/* Roles Overview */}
      <Box sx={{ 
        display: 'flex', 
        gap: 3, 
        mb: 4,
        flexDirection: { xs: 'column', md: 'row' }
      }}>
        <Box sx={{ flex: 2 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Roles
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Role Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Users</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {roles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <SecurityIcon color="action" fontSize="small" />
                            <Typography variant="body2" fontWeight={600}>
                              {role.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {role.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${role.userCount} users`}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={role.isDefault ? "Default" : "Custom"}
                            size="small"
                            color={role.isDefault ? "default" : "primary"}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            onClick={() => handleEditRole(role)}
                            disabled={role.isDefault}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            onClick={() => handleDeleteRole(role.id)}
                            disabled={role.isDefault || role.userCount > 0}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Box>
        
        <Box sx={{ flex: 1 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Permission Overview
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Available permissions in this workspace:
              </Typography>
              <Stack spacing={1}>
                {availablePermissions.slice(0, 6).map((permission) => (
                  <Box key={permission.key} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CheckIcon color="success" fontSize="small" />
                    <Typography variant="body2">
                      {permission.label}
                    </Typography>
                  </Box>
                ))}
                <Typography variant="caption" color="text.secondary">
                  And {availablePermissions.length - 6} more permissions...
                </Typography>
              </Stack>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Permission Matrix */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Permission Matrix
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Overview of permissions for each role:
          </Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Permission</TableCell>
                  {roles.map((role) => (
                    <TableCell key={role.id} align="center">
                      {role.name}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {availablePermissions.map((permission) => (
                  <TableRow key={permission.key}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {permission.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {permission.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    {roles.map((role) => (
                      <TableCell key={role.id} align="center">
                        {role.permissions.includes(permission.key) ? (
                          <CheckIcon color="success" fontSize="small" />
                        ) : (
                          <CancelIcon color="disabled" fontSize="small" />
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Create Role Dialog */}
      <Dialog open={createRoleDialogOpen} onClose={() => setCreateRoleDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Custom Role</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Role Name"
              value={roleForm.name}
              onChange={(e) => setRoleForm({ ...roleForm, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Description"
              value={roleForm.description}
              onChange={(e) => setRoleForm({ ...roleForm, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Permissions
              </Typography>
              <List dense>
                {availablePermissions.map((permission) => (
                  <ListItem key={permission.key} divider>
                    <ListItemIcon>
                      <Checkbox
                        checked={roleForm.permissions.includes(permission.key)}
                        onChange={() => handlePermissionToggle(permission.key)}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={permission.label}
                      secondary={permission.description}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateRoleDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateRole} variant="contained">
            Create Role
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog open={editRoleDialogOpen} onClose={() => setEditRoleDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Role</DialogTitle>
        <DialogContent>
          {selectedRole?.isDefault && (
            <Alert severity="info" sx={{ mb: 2 }}>
              This is a default role. You can only view its permissions.
            </Alert>
          )}
          <Stack spacing={3} sx={{ mt: 1 }}>
            <TextField
              label="Role Name"
              value={roleForm.name}
              onChange={(e) => setRoleForm({ ...roleForm, name: e.target.value })}
              fullWidth
              disabled={selectedRole?.isDefault}
            />
            <TextField
              label="Description"
              value={roleForm.description}
              onChange={(e) => setRoleForm({ ...roleForm, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
              disabled={selectedRole?.isDefault}
            />
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Permissions
              </Typography>
              <List dense>
                {availablePermissions.map((permission) => (
                  <ListItem key={permission.key} divider>
                    <ListItemIcon>
                      <Checkbox
                        checked={roleForm.permissions.includes(permission.key)}
                        onChange={() => handlePermissionToggle(permission.key)}
                        disabled={selectedRole?.isDefault}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={permission.label}
                      secondary={permission.description}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditRoleDialogOpen(false)}>Cancel</Button>
          {!selectedRole?.isDefault && (
            <Button onClick={handleUpdateRole} variant="contained">
              Update Role
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Container>
  );
}
