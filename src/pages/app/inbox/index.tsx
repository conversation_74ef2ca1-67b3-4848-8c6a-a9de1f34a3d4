import { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  Badge,
  IconButton,
  Button,
  Paper,
  Divider,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  Card,
  CardContent,
  Select,
  FormControl,
  InputLabel,
  Stack,
  Alert,
  Tooltip,
  Fade,
} from '@mui/material';
import {
  Search as SearchIcon,
  Inbox as InboxIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  FiberNew as NewIcon,
  Block as BlockIcon,
  Archive as ArchiveIcon,
  MoreVert as MoreVertIcon,
  Reply as ReplyIcon,
  Forward as ForwardIcon,
  Archive as ArchiveActionIcon,
  Delete as DeleteIcon,
  Attachment as AttachmentIcon,
  Send as SendIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  PersonAdd as PersonAddIcon,
} from '@mui/icons-material';

// Mock data for email threads
const mockThreads = [
  {
    id: 1,
    subject: 'Help with payment issue',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    channelEmail: '<EMAIL>',
    lastMessage: 'I was trying to update my credit card but the form keeps giving me an error...',
    lastActivity: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
    unread: true,
    assignedTo: null,
    status: 'open',
    priority: 'normal',
    messageCount: 3,
  },
  {
    id: 2,
    subject: 'Product demo request',
    customerName: 'Sarah Johnson',
    customerEmail: '<EMAIL>',
    channelEmail: '<EMAIL>',
    lastMessage: 'We would like to schedule a demo for our team of 20 developers...',
    lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    unread: false,
    assignedTo: 'Alice Smith',
    status: 'open',
    priority: 'high',
    messageCount: 2,
  },
  {
    id: 3,
    subject: 'Integration documentation',
    customerName: 'Mike Chen',
    customerEmail: '<EMAIL>',
    channelEmail: '<EMAIL>',
    lastMessage: 'Is there updated documentation for the REST API integration?',
    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    unread: false,
    assignedTo: 'Bob Wilson',
    status: 'open',
    priority: 'normal',
    messageCount: 1,
  },
  {
    id: 4,
    subject: 'Billing inquiry',
    customerName: 'Emma Davis',
    customerEmail: '<EMAIL>',
    channelEmail: '<EMAIL>',
    lastMessage: 'I noticed a charge on my account that I do not recognize...',
    lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    unread: false,
    assignedTo: null,
    status: 'pending',
    priority: 'normal',
    messageCount: 1,
  },
];

// Mock messages for threads
const mockMessages = [
  {
    id: 1,
    threadId: 1,
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Help with payment issue',
    body: `Hi there,

I was trying to update my credit card information in my account settings, but the form keeps giving me an error saying "Invalid card number" even though I have double-checked the number multiple times.

Could you please help me resolve this issue?

Thanks,
John`,
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    isInbound: true,
    attachments: [],
  },
  {
    id: 2,
    threadId: 1,
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Re: Help with payment issue',
    body: `Hi John,

Thank you for reaching out. I would be happy to help you with your payment issue.

Could you please try the following steps:
1. Clear your browser cache
2. Try using an incognito/private browser window
3. Make sure you are entering the card number without spaces or dashes

If the issue persists, please let me know which browser you are using and I will investigate further.

Best regards,
Support Team`,
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
    isInbound: false,
    attachments: [],
  },
  {
    id: 3,
    threadId: 1,
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Re: Help with payment issue',
    body: `Hi,

I tried all the steps you mentioned but I am still getting the same error. I am using Chrome version 96.

Is there anything else I can try?

John`,
    timestamp: new Date(Date.now() - 2 * 60 * 1000),
    isInbound: true,
    attachments: [],
  },
];

// Mock team members for assignment
const mockTeamMembers = [
  { id: '1', name: 'Alice Smith', email: '<EMAIL>', avatar: null },
  { id: '2', name: 'Bob Wilson', email: '<EMAIL>', avatar: null },
  { id: '3', name: 'Carol Johnson', email: '<EMAIL>', avatar: null },
];

// Mock channels
const mockChannels = [
  { id: '1', email: '<EMAIL>', name: 'Support' },
  { id: '2', email: '<EMAIL>', name: 'Sales' },
  { id: '3', email: '<EMAIL>', name: 'Billing' },
];

// Filter types
const FILTERS = {
  ALL: 'all',
  UNREAD: 'unread',
  ASSIGNED: 'assigned',
  UNASSIGNED: 'unassigned',
  SPAM: 'spam',
  CLOSED: 'closed',
} as const;

type FilterType = typeof FILTERS[keyof typeof FILTERS];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
    </div>
  );
}

// Human-readable time formatting
const formatHumanTime = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  // Same day
  if (days === 0) {
    return `Today at ${date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit', 
      hour12: true 
    })}`;
  }
  
  // Yesterday
  if (days === 1) {
    return `Yesterday at ${date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit', 
      hour12: true 
    })}`;
  }
  
  // This week
  if (days < 7) {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short',
      hour: 'numeric', 
      minute: '2-digit', 
      hour12: true 
    });
  }
  
  // Older
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
};

export default function InboxPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>(FILTERS.ALL);
  const [selectedChannel, setSelectedChannel] = useState<string>('all');
  const [selectedThread, setSelectedThread] = useState<number | null>(1);
  const [tabValue, setTabValue] = useState(0);
  const [replyText, setReplyText] = useState('');
  const [moreMenuAnchor, setMoreMenuAnchor] = useState<null | HTMLElement>(null);
  const [sendMenuAnchor, setSendMenuAnchor] = useState<null | HTMLElement>(null);
  const [sendingStatus, setSendingStatus] = useState<'idle' | 'sending' | 'sent' | 'failed'>('idle');

  // Filtered threads based on current filters
  const filteredThreads = mockThreads.filter(thread => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      if (!thread.subject.toLowerCase().includes(searchLower) &&
          !thread.customerName.toLowerCase().includes(searchLower) &&
          !thread.customerEmail.toLowerCase().includes(searchLower) &&
          !thread.lastMessage.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Status filter
    switch (selectedFilter) {
      case FILTERS.UNREAD:
        return thread.unread;
      case FILTERS.ASSIGNED:
        return thread.assignedTo !== null;
      case FILTERS.UNASSIGNED:
        return thread.assignedTo === null;
      case FILTERS.SPAM:
        return thread.status === 'spam';
      case FILTERS.CLOSED:
        return thread.status === 'closed';
      default:
        return thread.status !== 'spam' && thread.status !== 'closed';
    }
  }).filter(thread => {
    // Channel filter
    if (selectedChannel === 'all') return true;
    return thread.channelEmail === selectedChannel;
  });

  // Get messages for selected thread
  const threadMessages = selectedThread ? mockMessages.filter(msg => msg.threadId === selectedThread) : [];
  const currentThread = selectedThread ? mockThreads.find(t => t.id === selectedThread) : null;

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  const handleAssignTo = (memberId: string | null) => {
    if (selectedThread) {
      // Update assignment (in real app, this would be an API call)
      console.log(`Assigning thread ${selectedThread} to member ${memberId}`);
    }
  };

  const handleSendReply = async (action: 'send' | 'send-close' | 'send-assign' = 'send') => {
    if (replyText.trim() && selectedThread) {
      setSendingStatus('sending');
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log(`Sending reply with action: ${action}`, replyText);
        setSendingStatus('sent');
        setReplyText('');
        setSendMenuAnchor(null);
        
        // Reset status after 2 seconds
        setTimeout(() => setSendingStatus('idle'), 2000);
      } catch (error) {
        setSendingStatus('failed');
        setTimeout(() => setSendingStatus('idle'), 3000);
      }
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if ((event.metaKey || event.ctrlKey) && event.key === 'Enter') {
      event.preventDefault();
      handleSendReply();
    }
  };

  const getChannelBadgeColor = (channelEmail: string) => {
    const channel = mockChannels.find(c => c.email === channelEmail);
    switch (channel?.name) {
      case 'Support': return 'primary';
      case 'Sales': return 'success';
      case 'Billing': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'normal': return 'default';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Sidebar */}
      <Paper 
        elevation={0} 
        sx={{ 
          width: 280, 
          display: 'flex', 
          flexDirection: 'column',
          borderRadius: 0,
          borderRight: 2,
          borderColor: 'divider',
          backgroundColor: 'grey.50'
        }}
      >
        {/* Search */}
        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Filters */}
        <Box sx={{ px: 2, pb: 1 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
            Filter by status
          </Typography>
          <Stack spacing={0.5}>
            {[
              { key: FILTERS.ALL, label: 'All Conversations', icon: <InboxIcon /> },
              { key: FILTERS.UNREAD, label: 'Unread', icon: <NewIcon /> },
              { key: FILTERS.ASSIGNED, label: 'Assigned', icon: <AssignmentIcon /> },
              { key: FILTERS.UNASSIGNED, label: 'Unassigned', icon: <PersonIcon /> },
              { key: FILTERS.SPAM, label: 'Spam', icon: <BlockIcon /> },
              { key: FILTERS.CLOSED, label: 'Closed', icon: <ArchiveIcon /> },
            ].map((filter) => (
              <Button
                key={filter.key}
                variant={selectedFilter === filter.key ? 'contained' : 'text'}
                size="small"
                startIcon={filter.icon}
                onClick={() => setSelectedFilter(filter.key)}
                sx={{ 
                  justifyContent: 'flex-start',
                  color: selectedFilter === filter.key ? 'white' : 'text.primary',
                  '&:hover': {
                    backgroundColor: selectedFilter === filter.key ? 'primary.dark' : 'action.hover'
                  }
                }}
              >
                {filter.label}
              </Button>
            ))}
          </Stack>
        </Box>

        <Divider />

        {/* Channel Filter */}
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
            Filter by channel
          </Typography>
          <FormControl fullWidth size="small">
            <Select
              value={selectedChannel}
              onChange={(e) => setSelectedChannel(e.target.value)}
              displayEmpty
            >
              <MenuItem value="all">All Channels</MenuItem>
              {mockChannels.map((channel) => (
                <MenuItem key={channel.id} value={channel.email}>
                  {channel.name} ({channel.email})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {/* Thread List */}
      <Paper 
        elevation={0} 
        sx={{ 
          width: 400, 
          display: 'flex', 
          flexDirection: 'column',
          borderRadius: 0,
          borderRight: 2,
          borderColor: 'divider'
        }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">
            Conversations ({filteredThreads.length})
          </Typography>
        </Box>

        <List sx={{ flexGrow: 1, overflow: 'auto' }}>
          {filteredThreads.length === 0 ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Stack alignItems="center" spacing={2}>
                <InboxIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
                <Typography variant="h6" color="text.secondary">
                  No conversations found
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {searchTerm ? 'Try adjusting your search terms' : 'All caught up! No messages to show'}
                </Typography>
              </Stack>
            </Box>
          ) : (
            filteredThreads.map((thread) => (
            <ListItem 
              key={thread.id} 
              disablePadding 
              sx={{ 
                borderBottom: 1, 
                borderColor: 'divider',
                backgroundColor: selectedThread === thread.id ? 'action.selected' : 'transparent',
                '&:hover': {
                  backgroundColor: selectedThread === thread.id ? 'action.selected' : 'action.hover'
                }
              }}
            >
              <ListItemButton 
                onClick={() => setSelectedThread(thread.id)}
                sx={{ py: 2.5, px: 2 }}
              >
                <ListItemAvatar>
                  <Badge 
                    variant="dot" 
                    color="primary" 
                    invisible={!thread.unread}
                    anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                  >
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      {thread.customerName.charAt(0)}
                    </Avatar>
                  </Badge>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography 
                        variant="subtitle2" 
                        sx={{ 
                          fontWeight: 700,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          flex: 1
                        }}
                      >
                        {thread.customerName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ ml: 1, flexShrink: 0 }}>
                        {formatTime(thread.lastActivity)}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 0.5 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontWeight: thread.unread ? 600 : 400,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          mb: 0.5
                        }}
                      >
                        {thread.subject}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        color="text.secondary"
                        sx={{ 
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          display: 'block'
                        }}
                      >
                        {thread.lastMessage}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 1, flexWrap: 'wrap' }}>
                        <Chip
                          label={mockChannels.find(c => c.email === thread.channelEmail)?.name || 'Unknown'}
                          size="small"
                          color={getChannelBadgeColor(thread.channelEmail) as any}
                          variant="filled"
                          sx={{ 
                            fontSize: '0.7rem',
                            height: 20,
                            borderRadius: '10px',
                            fontWeight: 500
                          }}
                        />
                        {thread.assignedTo && (
                          <Chip
                            label={thread.assignedTo}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ 
                              fontSize: '0.7rem',
                              height: 20,
                              borderRadius: '10px',
                              fontWeight: 500
                            }}
                          />
                        )}
                        {thread.priority !== 'normal' && (
                          <Chip
                            label={thread.priority.toUpperCase()}
                            size="small"
                            color={getPriorityColor(thread.priority) as any}
                            variant="outlined"
                            sx={{ 
                              fontSize: '0.7rem',
                              height: 20,
                              borderRadius: '10px',
                              fontWeight: 600
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Typography variant="caption" color="text.secondary">
                    {thread.messageCount}
                  </Typography>
                </ListItemSecondaryAction>
              </ListItemButton>
            </ListItem>
          ))
          )}
        </List>
      </Paper>

      {/* Thread View */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {selectedThread && currentThread ? (
          <>
            {/* Thread Header */}
            <Paper 
              elevation={0} 
              sx={{ 
                p: 3, 
                borderRadius: 0,
                borderBottom: 2,
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}
            >
              <Box sx={{ flex: 1 }}>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  {currentThread.subject}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {currentThread.customerName}
                  </Typography>
                  <Chip 
                    label={currentThread.status.toUpperCase()} 
                    size="small" 
                    color={currentThread.status === 'open' ? 'success' : 'default'}
                    variant="filled"
                    sx={{ fontSize: '0.7rem', height: 20 }}
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                  {currentThread.customerEmail}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Last activity: 3 minutes ago
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Assign to</InputLabel>
                  <Select
                    value={currentThread.assignedTo || ''}
                    onChange={(e) => handleAssignTo(e.target.value || null)}
                    label="Assign to"
                  >
                    <MenuItem value="">Unassigned</MenuItem>
                    {mockTeamMembers.map((member) => (
                      <MenuItem key={member.id} value={member.name}>
                        {member.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <IconButton onClick={(e) => setMoreMenuAnchor(e.currentTarget)}>
                  <MoreVertIcon />
                </IconButton>
                <Menu
                  anchorEl={moreMenuAnchor}
                  open={Boolean(moreMenuAnchor)}
                  onClose={() => setMoreMenuAnchor(null)}
                >
                  <MenuItem onClick={() => setMoreMenuAnchor(null)}>
                    <ArchiveActionIcon sx={{ mr: 1 }} />
                    Archive
                  </MenuItem>
                  <MenuItem onClick={() => setMoreMenuAnchor(null)}>
                    <BlockIcon sx={{ mr: 1 }} />
                    Mark as Spam
                  </MenuItem>
                  <MenuItem onClick={() => setMoreMenuAnchor(null)}>
                    <DeleteIcon sx={{ mr: 1 }} />
                    Delete
                  </MenuItem>
                </Menu>
              </Box>
            </Paper>

            {/* Messages */}
            <Box sx={{ flexGrow: 1, overflow: 'auto', p: 3 }}>
              <Stack spacing={3}>
                {threadMessages.map((message, index) => (
                  <Fade in={true} timeout={300 + index * 100} key={message.id}>
                    <Card 
                      variant="outlined"
                      sx={{ 
                        bgcolor: message.isInbound ? 'background.paper' : 'primary.50',
                        borderColor: message.isInbound ? 'divider' : 'primary.200',
                        boxShadow: message.isInbound ? 1 : 'none',
                        maxWidth: '85%',
                        ml: message.isInbound ? 0 : 'auto',
                        mr: message.isInbound ? 'auto' : 0,
                        alignSelf: message.isInbound ? 'flex-start' : 'flex-end'
                      }}
                    >
                      <CardContent sx={{ pb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ width: 36, height: 36, mr: 2 }}>
                            {message.isInbound ? currentThread.customerName.charAt(0) : 'S'}
                          </Avatar>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {message.isInbound ? currentThread.customerName : 'Support Team'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                              {message.isInbound ? currentThread.customerEmail : '<EMAIL>'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatHumanTime(message.timestamp)}
                            </Typography>
                          </Box>
                          {message.attachments.length > 0 && (
                            <AttachmentIcon color="action" />
                          )}
                        </Box>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            whiteSpace: 'pre-wrap',
                            fontFamily: 'inherit',
                            lineHeight: 1.6
                          }}
                        >
                          {message.body}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Fade>
                ))}
              </Stack>
            </Box>

            {/* Reply Composer */}
            <Paper 
              elevation={1} 
              sx={{ 
                p: 2,
                borderRadius: 0,
                borderTop: 1,
                borderColor: 'divider'
              }}
            >
              <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 2 }}>
                <Tab icon={<ReplyIcon />} label="Reply" />
                <Tab icon={<ForwardIcon />} label="Forward" />
              </Tabs>

              <TabPanel value={tabValue} index={0}>
                <Stack spacing={2}>
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    placeholder="Type your reply..."
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                    onKeyDown={handleKeyDown}
                    variant="outlined"
                    disabled={sendingStatus === 'sending'}
                  />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Tooltip title="Attach files">
                        <IconButton size="small">
                          <AttachmentIcon />
                        </IconButton>
                      </Tooltip>
                      <Typography variant="caption" color="text.secondary">
                        ⌘+Enter to send
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button 
                        variant="outlined" 
                        size="small"
                        sx={{ 
                          borderColor: 'grey.300',
                          color: 'text.secondary',
                          '&:hover': { borderColor: 'grey.400' }
                        }}
                      >
                        Save Draft
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        endIcon={sendingStatus === 'sending' ? null : <ArrowDownIcon />}
                        onClick={(e) => setSendMenuAnchor(e.currentTarget)}
                        disabled={!replyText.trim() || sendingStatus === 'sending'}
                        sx={{ 
                          fontWeight: 600,
                          minWidth: 80
                        }}
                      >
                        {sendingStatus === 'sending' ? 'Sending...' : 
                         sendingStatus === 'sent' ? <CheckIcon /> : 'Send'}
                      </Button>
                      <Menu
                        anchorEl={sendMenuAnchor}
                        open={Boolean(sendMenuAnchor)}
                        onClose={() => setSendMenuAnchor(null)}
                        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                        transformOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      >
                        <MenuItem onClick={() => handleSendReply('send')}>
                          <SendIcon sx={{ mr: 1 }} />
                          Send
                        </MenuItem>
                        <MenuItem onClick={() => handleSendReply('send-close')}>
                          <CloseIcon sx={{ mr: 1 }} />
                          Send & Close
                        </MenuItem>
                        <MenuItem onClick={() => handleSendReply('send-assign')}>
                          <PersonAddIcon sx={{ mr: 1 }} />
                          Send & Assign
                        </MenuItem>
                      </Menu>
                    </Box>
                  </Box>
                </Stack>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Alert severity="info">
                  Forward functionality would be implemented here.
                </Alert>
              </TabPanel>
            </Paper>
          </>
        ) : (
          <Box 
            sx={{ 
              flexGrow: 1, 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              color: 'text.secondary',
              p: 4
            }}
          >
            <Stack alignItems="center" spacing={3}>
              <EmailIcon sx={{ fontSize: 80, color: 'text.disabled' }} />
              <Typography variant="h5" color="text.secondary">
                Select a conversation
              </Typography>
              <Typography variant="body1" color="text.secondary" textAlign="center">
                Choose a conversation from the list to view and reply to messages
              </Typography>
            </Stack>
          </Box>
        )}
      </Box>
    </Box>
  );
}