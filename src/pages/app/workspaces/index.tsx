import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardA<PERSON>,
  CardContent,
  Container,
  Typography,
  Skeleton,
  Alert,
  Fab,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import {
  Add as AddIcon,
  FolderOpen as FolderOpenIcon,
} from "@mui/icons-material";
import CreateWorkspaceModal from "./components/CreateWorkspaceModal";
import WorkspaceCard from "./components/WorkspaceCard";
import { useWorkspaceStore } from "../../../stores/workSpaceStore";

export default function WorkspacesPage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  
  const workspaces = useWorkspaceStore((s) => s.workspaces);
  const selectedWorkspace = useWorkspaceStore((s) => s.selectedWorkspace);
  const setSelectedWorkspace = useWorkspaceStore((s) => s.setSelectedWorkspace);
  const refreshWorkspaces = useWorkspaceStore((s) => s.refreshWorkspaces);
  
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadWorkspaces = async () => {
      try {
        setLoading(true);
        setError(null);
        await refreshWorkspaces();
      } catch (err) {
        setError('Failed to load workspaces. Please try again.');
        console.error('Failed to load workspaces:', err);
      } finally {
        setLoading(false);
      }
    };

    loadWorkspaces();
  }, [refreshWorkspaces]);

  const handleWorkspaceSelect = (workspace: any) => {
    setSelectedWorkspace(workspace);
    navigate(`/app/w/${workspace.id}/`);
  };

  const EmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        textAlign: 'center',
      }}
    >
      <FolderOpenIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
      <Typography variant="h5" color="text.secondary" gutterBottom>
        No workspaces yet
      </Typography>
      <Typography variant="body1" color="text.disabled" sx={{ mb: 3, maxWidth: 400 }}>
        Create your first workspace to start organizing your projects and collaborate with your team.
      </Typography>
      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={() => setModalOpen(true)}
        size="large"
      >
        Create Your First Workspace
      </Button>
    </Box>
  );

  const LoadingSkeleton = () => (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: 3,
      }}
    >
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index}>
          <CardContent>
            <Skeleton variant="text" width="60%" height={32} />
            <Skeleton variant="text" width="40%" height={20} sx={{ mt: 1 }} />
          </CardContent>
          <CardActions>
            <Skeleton variant="rectangular" width={80} height={32} />
          </CardActions>
        </Card>
      ))}
    </Box>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h3" component="h1" gutterBottom>
            Workspaces
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your projects and collaborate with your team
          </Typography>
        </Box>
        {!isMobile && workspaces.length > 0 && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setModalOpen(true)}
            size="large"
          >
            Create Workspace
          </Button>
        )}
      </Box>

      {/* Error State */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Content */}
      {loading ? (
        <LoadingSkeleton />
      ) : workspaces.length === 0 ? (
        <EmptyState />
      ) : (
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: 3,
          }}        >
          {workspaces.map((workspace) => (
            <WorkspaceCard
              key={workspace.id}
              workspace={workspace}
              isSelected={selectedWorkspace?.id === workspace.id}
              onSelect={handleWorkspaceSelect}
              onEdit={() => {
                // TODO: Implement edit functionality
              }}
              onDelete={() => {
                // TODO: Implement delete functionality
              }}
              onSettings={() => {
                // Navigation is handled in WorkspaceCard component
              }}
            />
          ))}
        </Box>
      )}

      {/* Floating Action Button for Mobile */}
      {isMobile && workspaces.length > 0 && (
        <Fab
          color="primary"
          aria-label="create workspace"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
          }}
          onClick={() => setModalOpen(true)}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Create Workspace Modal */}
      <CreateWorkspaceModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
      />
    </Container>
  );
}
