import React from 'react';
import {
  Box,
  Typography,
  IconButton,
  Collapse,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
  CircularProgress
} from '@mui/material';
import {
  Folder,
  FolderOpen,
  ChevronRight,
  MoreVertical,
  Edit2,
  Trash2,
  FolderPlus,
  Home
} from 'lucide-react';
import { useKnowledgeBaseStore, KnowledgeBaseItem } from '../../../../stores/knowledgeBaseStore';
import { useNavigate, useParams } from 'react-router';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';

interface FolderTreeProps {
  parentId?: number | null;
  level?: number;
  onCreateFolder?: (parentId: number | null) => void;
  currentFolderId?: number | null;
}

function FolderTreeSkeleton({ level = 0 }: { level?: number }) {
  return (
    <Box>
      {[...Array(3)].map((_, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 1,
            py: 0.75,
            pl: level * 2 + 1,
          }}
        >
          <Box sx={{ width: 24, mr: 0.5 }} />
          <Skeleton variant="circular" width={18} height={18} sx={{ mr: 1 }} />
          <Skeleton variant="text" width={`${60 + Math.random() * 40}%`} height={20} />
        </Box>
      ))}
    </Box>
  );
}

interface FolderItemProps {
  item: KnowledgeBaseItem;
  level: number;
  onCreateFolder?: (parentId: number | null) => void;
  currentFolderId?: number | null;
}

function FolderItem({ item, level, onCreateFolder, currentFolderId }: FolderItemProps) {
  const {
    getItems,
    toggleFolder,
    addItem,
    deleteItem,
    isFolderLoaded,
    getItem,
    isDeleting,
  } = useKnowledgeBaseStore();

  const [contextMenu, setContextMenu] = React.useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);
  const [isToggling, setIsToggling] = React.useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);

  const navigate = useNavigate();
  const { workspaceId } = useParams();
  const childFolders = getItems(item.id, "folder");
  
  // Check if this folder is the current folder or in the path to current folder
  const isCurrentFolder = currentFolderId === item.id;
  
  // Helper function to check if current folder is a descendant of this folder
  const isInPathToCurrent = React.useMemo(() => {
    if (!currentFolderId || currentFolderId === item.id) return false;
    
    // Build path from current folder to root
    const getPath = (folderId: number): number[] => {
      const folder = getItem(folderId);
      if (!folder) return [];
      if (folder.parent_id === null) return [folderId];
      return [...getPath(folder.parent_id), folderId];
    };
    
    const currentPath = getPath(currentFolderId);
    return currentPath.includes(item.id);
  }, [currentFolderId, item.id, getItem]);
  
  // Track if we've auto-expanded this folder for the current path
  const [hasAutoExpanded, setHasAutoExpanded] = React.useState(false);
  
  // Auto-expand if this folder is in the path to current folder (but not the current folder itself)
  // Only auto-expand once per navigation, don't force it to stay expanded
  React.useEffect(() => {
    if (isInPathToCurrent && !item.isExpanded && !isToggling && !hasAutoExpanded) {
      toggleFolder(item.id);
      setHasAutoExpanded(true);
    }
    
    // Reset the auto-expand flag when this folder is no longer in the path
    if (!isInPathToCurrent) {
      setHasAutoExpanded(false);
    }
  }, [isInPathToCurrent, item.isExpanded, item.id, toggleFolder, isToggling, hasAutoExpanded]);

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setContextMenu({
      mouseX: event.clientX + 2,
      mouseY: event.clientY - 6,
    });
  };

  const handleClose = () => setContextMenu(null);

  const handleSelect = () => {
    navigate(`/app/w/${workspaceId}/knowledge/folder/${item.id}`);
  }

  const handleToggle = async (e: React.MouseEvent) => {
    e.stopPropagation();
    
    // If folder is being expanded and not loaded yet, show loading
    if (!item.isExpanded && !isFolderLoaded(item.id)) {
      setIsToggling(true);
    }
    
    await toggleFolder(item.id);
    setIsToggling(false);
  };

  const handleAddSubfolder = async () => {
    if (onCreateFolder) {
      onCreateFolder(item.id);
    } else {
      const name = prompt('Enter folder name:');
      if (name) await addItem(name, "folder", item.id);
    }
    handleClose();
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
    handleClose();
  };

  const handleConfirmDelete = async () => {
    await deleteItem(item.id);
    setDeleteDialogOpen(false);
  };

  const hasChildren = childFolders.length > 0;

  return (
    <Box>
      <Box
        className="folder-item"
        sx={{
          display: 'flex',
          alignItems: 'center',
          px: 1,
          py: 0.75,
          pl: level * 2 + 1,
          cursor: 'pointer',
          bgcolor: isCurrentFolder ? 'action.selected' : 'transparent',
          color: isCurrentFolder ? 'primary.main' : 'text.primary',
          borderRadius: 1,
          transition: 'background-color 0.2s ease',
          '&:hover': {
            bgcolor: isCurrentFolder ? 'action.selected' : 'action.hover',
          },
          position: 'relative',
          fontWeight: isCurrentFolder ? 600 : 400,
        }}
        onClick={handleSelect}
        onContextMenu={handleContextMenu}
      >
        {hasChildren ? (
          <IconButton
            size="small"
            onClick={handleToggle}
            disabled={isToggling}
            sx={{
              p: 0.5,
              mr: 0.5,
              color: 'text.secondary',
              transition: 'transform 0.2s',
              transform: item.isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
            }}
          >
            {isToggling ? (
              <CircularProgress size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
          </IconButton>
        ) : (
          <Box sx={{ width: 24, mr: 0.5 }} />
        )}

        {item.isExpanded ? (
          <FolderOpen size={18} style={{ marginRight: 8 }} />
        ) : (
          <Folder size={18} style={{ marginRight: 8 }} />
        )}

        <Typography
          variant="body2"
          sx={{
            flex: 1,
            fontSize: '0.875rem',
            fontWeight: isCurrentFolder ? 600 : 400,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
        >
          {item.title}
        </Typography>

        <IconButton
          size="small"
          onClick={handleContextMenu}
          sx={{
            p: 0.25,
            opacity: 0,
            position: 'absolute',
            right: 8,
            transition: 'opacity 0.2s',
            '.folder-item:hover &': {
              opacity: 1
            }
          }}
        >
          <MoreVertical size={14} />
        </IconButton>
      </Box>

      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        PaperProps={{
          sx: {
            borderRadius: 1,
            minWidth: 180,
            boxShadow: 3,
            fontSize: '0.875rem'
          }
        }}
      >
        <MenuItem onClick={handleAddSubfolder}>
          <ListItemIcon><FolderPlus size={16} /></ListItemIcon>
          <ListItemText>Add Subfolder</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { /* TODO: Rename */ handleClose(); }}>
          <ListItemIcon><Edit2 size={16} /></ListItemIcon>
          <ListItemText>Rename</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
          <ListItemIcon><Trash2 size={16} /></ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        itemType="folder"
        itemTitle={item.title}
        isLoading={isDeleting}
      />

      <Collapse in={item.isExpanded}>
        <FolderTree parentId={item.id} level={level + 1} onCreateFolder={onCreateFolder} currentFolderId={currentFolderId} />
      </Collapse>
    </Box>
  );
}

export default function FolderTree({ parentId = null, level = 0, onCreateFolder, currentFolderId: propCurrentFolderId }: FolderTreeProps) {
  const { getItems, isLoading, loadFolderContents, isFolderLoaded } = useKnowledgeBaseStore();
  const navigate = useNavigate();
  const params = useParams();
  const { workspaceId } = useParams();
  const [isLoadingThisFolder, setIsLoadingThisFolder] = React.useState(false);
  
  // Get current folder ID from URL params if not passed as prop
  const currentFolderId = propCurrentFolderId ?? (params.folderId ? parseInt(params.folderId) : null);
  
  const folders = getItems(parentId, "folder");

  // Load folder contents if not already loaded
  React.useEffect(() => {
    const loadFolderData = async () => {
      if (!isFolderLoaded(parentId)) {
        setIsLoadingThisFolder(true);
        await loadFolderContents(parentId);
        setIsLoadingThisFolder(false);
      }
    };

    if (level > 0) { // Don't auto-load root level, it's handled by loadItems
      loadFolderData();
    }
  }, [parentId, level, loadFolderContents, isFolderLoaded]);

  const handleAllContentClick = () => {
    navigate(`/app/w/${workspaceId}/knowledge`);
  };

  // Check if we're on the "All Content" page (no folder selected)
  const isAllContentActive = currentFolderId === null;

  // Show skeleton loading for initial load or folder loading
  if ((isLoading && parentId === null && level === 0) || (isLoadingThisFolder && level > 0)) {
    return (
      <Box className="folder-tree">
        <FolderTreeSkeleton level={level} />
      </Box>
    );
  }

  return (
    <Box className="folder-tree">
      {/* Show "All Content" entry only at the root level */}
      {parentId === null && level === 0 && (
        <Box
          className="all-content-item"
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 1,
            py: 0.75,
            pl: level * 2 + 1,
            cursor: 'pointer',
            bgcolor: isAllContentActive ? 'action.selected' : 'transparent',
            color: isAllContentActive ? 'primary.main' : 'text.primary',
            borderRadius: 1,
            transition: 'background-color 0.2s ease',
            '&:hover': {
              bgcolor: isAllContentActive ? 'action.selected' : 'action.hover',
            },
            position: 'relative',
            mb: 1,
          }}
          onClick={handleAllContentClick}
        >
          <Box sx={{ width: 24, mr: 0.5 }} />
          <Home size={18} style={{ marginRight: 8 }} />
          <Typography
            variant="body2"
            sx={{
              flex: 1,
              fontSize: '1rem',
              fontWeight: 600,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            All Content
          </Typography>
        </Box>
      )}
      
      {folders.map((folder) => (
        <FolderItem key={folder.id} item={folder} level={level} onCreateFolder={onCreateFolder} currentFolderId={currentFolderId} />
      ))}
    </Box>
  );
}
