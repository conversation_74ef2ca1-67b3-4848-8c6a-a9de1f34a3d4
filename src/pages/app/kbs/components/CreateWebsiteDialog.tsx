/*
    1- The dialog submits a crawling_request to the server with the provided URL.
    2- The server crawls the website and returns the pages directly.
    3- The crawled pages are organized in a tree structure based on their hierarchy.
    4- The user can select the pages they want to import, and the dialog will create articles for each selected page.
    5- The dialog will then close, and the imported articles will be available in the knowledge base.
*/

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Checkbox,
  FormControlLabel,
  Alert,
  Collapse,
  IconButton
} from '@mui/material';
import { Globe, ChevronRight, ChevronDown, Folder, FileText } from 'lucide-react';
import { 
  crawlWebsite,
  getSelectedPagesForBulkCreate,
  type CrawledPage
} from '../../../../mocks';
import { useKnowledgeBaseStore } from '../../../../stores/knowledgeBaseStore';

interface CreateWebsiteDialogProps {
  open: boolean;
  onClose: () => void;
  parentId?: number | null;
}

type DialogStep = 'input' | 'selection' | 'importing';

export default function CreateWebsiteDialog({ open, onClose, parentId }: CreateWebsiteDialogProps) {
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [currentStep, setCurrentStep] = useState<DialogStep>('input');
  const [crawledPages, setCrawledPages] = useState<CrawledPage[]>([]);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);
  const [isCrawling, setIsCrawling] = useState(false);
  
  const { bulkCreateFromCrawl } = useKnowledgeBaseStore();

  // Reset dialog state when opening/closing
  useEffect(() => {
    if (!open) {
      setCurrentStep('input');
      setIsCrawling(false);
      setCrawledPages([]);
      setError(null);
      setExpandedItems(new Set());
    } else {
      // Set default expanded items when dialog is opened
      setExpandedItems(getDefaultExpandedItems(crawledPages));
    }
  }, [open]);

  const handleClose = () => {
    setTitle('');
    setUrl('');
    setCurrentStep('input');
    setIsCrawling(false);
    setCrawledPages([]);
    setError(null);
    onClose();
  };

  const handleStartCrawling = async () => {
    if (!url.trim()) return;
    
    try {
      setError(null);
      setIsCrawling(true);
      
      // Call the direct crawling API
      const pages = await crawlWebsite(url);
      setCrawledPages(pages);
      
      // Set default expanded items
      setExpandedItems(getDefaultExpandedItems(pages));
      
      setCurrentStep('selection');
    } catch (err) {
      setError('Failed to crawl website. Please check the URL and try again.');
    } finally {
      setIsCrawling(false);
    }
  };

  const handlePageSelection = (pageId: string, selected: boolean) => {
    const updatePageSelection = (pages: CrawledPage[]): CrawledPage[] => {
      return pages.map(page => {
        if (page.id === pageId) {
          // Update this page and all its children recursively
          const updateChildren = (childPages: CrawledPage[]): CrawledPage[] => {
            return childPages.map(child => ({
              ...child,
              selected,
              children: child.children ? updateChildren(child.children) : []
            }));
          };
          
          return { 
            ...page, 
            selected,
            children: page.children ? updateChildren(page.children) : []
          };
        }
        if (page.children) {
          const updatedChildren = updatePageSelection(page.children);
          // Don't update parent selection automatically - only when explicitly selected
          return { 
            ...page, 
            children: updatedChildren
          };
        }
        return page;
      });
    };
    
    setCrawledPages(updatePageSelection(crawledPages));
  };

  // Helper function to determine if a page has mixed selection state (for indeterminate checkboxes)
  const getSelectionState = (page: CrawledPage): { checked: boolean; indeterminate: boolean } => {
    if (!page.children || page.children.length === 0) {
      return { checked: page.selected, indeterminate: false };
    }
    
    const childStates = page.children.map(child => getSelectionState(child));
    const allSelected = childStates.every(state => state.checked && !state.indeterminate);
    const someSelected = childStates.some(state => state.checked || state.indeterminate);
    
    return {
      checked: allSelected,
      indeterminate: someSelected && !allSelected
    };
  };

  const handleToggleExpand = (pageId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(pageId)) {
      newExpanded.delete(pageId);
    } else {
      newExpanded.add(pageId);
    }
    setExpandedItems(newExpanded);
  };

  const handleImportSelected = async () => {
    try {
      setCurrentStep('importing');
      setError(null);
      
      const selectedPages = getSelectedPagesForBulkCreate(crawledPages);
      if (selectedPages.length === 0) {
        setError('Please select at least one page to import.');
        setCurrentStep('selection');
        return;
      }
      
      // Use bulk create to handle folder structure properly
      await bulkCreateFromCrawl(crawledPages, parentId || null, crawledPages[0]?.url || '');
      
    // Refresh the folder contents to show new articles and folders
    //   await refreshFolderContents(parentId || null);
      
      handleClose();
    } catch (err) {
      setError('Failed to import selected pages. Please try again.');
      setCurrentStep('selection');
    }
  };

  const canProceed = () => {
    return url.trim().length > 0;
  };

  // Helper component to render page tree
  const renderPageTree = (pages: CrawledPage[], depth = 0) => {
    return pages.map((page) => {
      const selectionState = getSelectionState(page);
      
      return (
        <Box key={page.id} sx={{ ml: depth * 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', py: 0.5 }}>
            {page.children && page.children.length > 0 && (
              <IconButton
                size="small"
                onClick={() => handleToggleExpand(page.id)}
                sx={{ mr: 0.5, width: 24, height: 24 }}
              >
                {expandedItems.has(page.id) ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </IconButton>
            )}
            {(!page.children || page.children.length === 0) && (
              <Box sx={{ width: 24, height: 24, mr: 0.5 }} />
            )}
            
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectionState.checked}
                  indeterminate={selectionState.indeterminate}
                  onChange={(e) => handlePageSelection(page.id, e.target.checked)}
                  size="small"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {page.children && page.children.length > 0 ? (
                    <Folder size={16} />
                  ) : (
                    <FileText size={16} />
                  )}
                  <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                    {page.title}
                  </Typography>
                </Box>
              }
              sx={{ flex: 1, ml: 0, mr: 0 }}
            />
          </Box>
          
          {page.children && page.children.length > 0 && expandedItems.has(page.id) && (
            <Collapse in={expandedItems.has(page.id)}>
              {renderPageTree(page.children, depth + 1)}
            </Collapse>
          )}
        </Box>
      );
    });
  };

  const getDialogTitle = () => {
    switch (currentStep) {
      case 'input':
        return 'Import Website Content';
      case 'selection':
        return 'Select Pages to Import';
      case 'importing':
        return 'Importing Articles...';
      default:
        return 'Import Website Content';
    }
  };

  const getActionButtons = () => {
    switch (currentStep) {
      case 'input':
        return (
          <>
            <Button onClick={handleClose}>Cancel</Button>
            <Button 
              onClick={handleStartCrawling} 
              variant="contained"
              disabled={!canProceed() || isCrawling}
            >
              {isCrawling ? 'Crawling...' : 'Start Crawling'}
            </Button>
          </>
        );
      case 'selection':
      case 'importing':
        return (
          <>
            <Button onClick={handleClose}>Cancel</Button>
            <Button 
              onClick={handleImportSelected} 
              variant="contained"
              disabled={getSelectedPagesForBulkCreate(crawledPages).length === 0 || currentStep === 'importing' || isCrawling}
            >
                {currentStep === 'importing' ? 'Importing...' : `Import Selected (${getSelectedPagesForBulkCreate(crawledPages).length})`}
            </Button>
          </>
        );
     
      default:
        return null;
    }
  };

  // Helper function to determine which items should be expanded by default
  const getDefaultExpandedItems = (pages: CrawledPage[]): Set<string> => {
    const expanded = new Set<string>();
    
    // Always expand the first root folder (home page)
    if (pages.length > 0) {
      expanded.add(pages[0].id);
    }
    
    // Find and expand the first folder with children (usually docs or similar)
    const findFirstFolderWithChildren = (pageList: CrawledPage[]): void => {
      for (const page of pageList) {
        if (page.children && page.children.length > 0) {
          expanded.add(page.id);
          // Also expand the first child folder if it exists
          if (page.children[0]?.children && page.children[0].children.length > 0) {
            expanded.add(page.children[0].id);
          }
          return; // Only expand the first folder with children
        }
      }
    };
    
    findFirstFolderWithChildren(pages);
    
    return expanded;
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Globe size={20} />
          {getDialogTitle()}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {currentStep === 'input' && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              autoFocus
              fullWidth
              label="Website URL"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              variant="outlined"
              helperText="Enter the URL of the website you want to crawl and import"
            />
            <TextField
              fullWidth
              label="Title (Optional)"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Leave empty to use page titles"
              variant="outlined"
            />
            <Typography variant="body2" color="text.secondary">
              Import content from a website URL. The crawler will discover pages and allow you to select which ones to import as articles.
            </Typography>
          </Box>
        )}

        {((currentStep === 'selection' && crawledPages.length > 0) || (currentStep === 'importing')) && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <Typography variant="h6">
              Select pages to import:
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Choose which pages you want to import. Folders will be created automatically for sections with multiple pages, 
              and individual pages will become articles. The folder structure will mirror the website hierarchy.
            </Typography>
            <Box 
              sx={{ 
                maxHeight: 400, 
                overflow: 'auto',
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                p: 1
              }}
            >
              {renderPageTree(crawledPages)}
            </Box>
          </Box>
        )}

        
      </DialogContent>
      
      <DialogActions>
        {getActionButtons()}
      </DialogActions>
    </Dialog>
  );
}
