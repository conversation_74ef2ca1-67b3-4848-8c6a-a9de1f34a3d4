import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Drawer,
  IconButton,
  <PERSON>ton,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from "@mui/material";
import { Edit, Maximize2, X } from "lucide-react";
import { useKnowledgeBaseStore } from "../../../../stores/knowledgeBaseStore";
import { Crepe } from "@milkdown/crepe";
import { Milkdown, MilkdownProvider, useEditor } from "@milkdown/react";
import { useSearchParams } from "react-router";
import "@milkdown/crepe/theme/common/style.css";
import "@milkdown/crepe/theme/frame.css";

export default function ArticleEditor() {
  const { getItem, updateItem } = useKnowledgeBaseStore();

  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [mode, setMode] = useState("view"); // "edit" or "view"
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [open, setOpen] = useState(false);
  const [editor, setEditor] = useState<Crepe | null>(null);
  const [searchParams, setSearchParams] = useSearchParams();
  const [fullScreen, setFullScreen] = useState(false);
  const [currentArticle, setCurrentArticle] = useState<any>(null);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<"close" | "cancel" | null>(null);
  const [editorKey, setEditorKey] = useState(0); // Force editor re-render

  useEffect(() => {
    const articleId = searchParams.get("articleId");
    const _mode = searchParams.get("mode") || "view";
    setMode(_mode);
    if (articleId) {
      setOpen(true);
      const article = getItem(parseInt(articleId));
      if (article && article.type === "article") {
        setCurrentArticle(article);
        setTitle(article.title);
        setContent(article.content);
        setHasUnsavedChanges(false);
        setEditorKey(prev => prev + 1); // Force editor re-render with new content
        
        // For non-editor sources, force view mode
        if (article.source !== "editor" && _mode === "edit") {
          setMode("view");
          setSearchParams((prev) => {
            const params = new URLSearchParams(prev);
            params.set("mode", "view");
            return params;
          });
        }
      } else {
        console.error("Article not found:", articleId);
      }
    }
  }, [searchParams, getItem, setSearchParams]);

  const handleSave = () => {
    if (currentArticle) {
      updateItem(currentArticle.id, {
        title,
        content,
      });
      setHasUnsavedChanges(false);
    }
  };

  const toggleDrawer = (open: boolean) => () => {
    if (!open && hasUnsavedChanges) {
      // Show confirmation dialog
      setShowUnsavedDialog(true);
      setPendingAction("close");
      return;
    }
    
    setOpen(open);
    if (!open) {
      setCurrentArticle(null); // Clear selected article when closing drawer
      setSearchParams({}); // Clear search params when closing
    }
  };

  const toggleMode = (mode: "view" | "edit") => {
    if (mode === "view" && hasUnsavedChanges) {
      // Show confirmation dialog
      setShowUnsavedDialog(true);
      setPendingAction("cancel");
      return;
    }
    
    setMode(mode);
    setSearchParams((prev) => {
      const params = new URLSearchParams(prev);
      params.set("mode", mode);
      return params;
    });
    setHasUnsavedChanges(false);
    editor?.setReadonly(mode === "view");
  };

  const handleUnsavedDialogClose = () => {
    setShowUnsavedDialog(false);
    setPendingAction(null);
  };

  const handleDiscardChanges = () => {
    setHasUnsavedChanges(false);
    setShowUnsavedDialog(false);
    
    if (pendingAction === "close") {
      setOpen(false);
      setCurrentArticle(null);
      setSearchParams({});
    } else if (pendingAction === "cancel") {
      // Reset content to original
      if (currentArticle) {
        setTitle(currentArticle.title);
        setContent(currentArticle.content);
        // Force editor to re-render with original content
        setEditorKey(prev => prev + 1);
      }
      setMode("view");
      setSearchParams((prev) => {
        const params = new URLSearchParams(prev);
        params.set("mode", "view");
        return params;
      });
      editor?.setReadonly(true);
    }
    
    setPendingAction(null);
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={toggleDrawer(false)}
      hideBackdrop
    >
      <div style={{ width: fullScreen ? "100vw" : "55vw", height: "100vh" }}>
      <Paper
        elevation={0}
        sx={{
          py: 1,
          px: 2,
          borderRadius: 0,
          display: "flex",
          alignItems: "center",
          borderBottom: 1,
          borderColor: "divider",
          position: "sticky",
          top: 0,
          zIndex: 1000,
        }}
      >
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          {mode === "view" ? "View Article" : "Edit Article"}
          {currentArticle && currentArticle.source !== "editor" && (
            <Typography variant="caption" sx={{ ml: 1, color: "text.secondary" }}>
              ({currentArticle.source} - read only)
            </Typography>
          )}
        </Typography>

        <Box sx={{ display: "flex", gap: 1 }}>
          {mode === "view" ? (
            <IconButton
              color="primary"
              onClick={() => {
                toggleMode("edit");
              }}
              size="small"
              disabled={currentArticle?.source !== "editor"}
            >
              <Edit size={20} />
            </IconButton>
          ) : (
            <>
              <Button
                onClick={() => {
                  toggleMode("view");
                }}
                size="small"
                variant="outlined"
                color="secondary"
              >
                <Typography variant="button">Cancel</Typography>
              </Button>
              <Button
                onClick={handleSave}
                size="small"
                variant="contained"
                color="primary"
                disabled={!hasUnsavedChanges}
              >
                <Typography variant="button">Save</Typography>
              </Button>
            </>
          )}
          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
          <IconButton onClick={() => setFullScreen(!fullScreen)} size="small">
            <Maximize2 size={18} />
          </IconButton>
          <IconButton onClick={toggleDrawer(false)} size="small">
            <X size={18} />
          </IconButton>
        </Box>
      </Paper>

      <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
        {/* Content Editor */}
        <Box sx={{ flex: 1, display: "flex", overflow: "hidden" }}>
          <div style={{ flex: 1, overflow: "auto" }}>
            <MilkdownEditorWrapper
              key={editorKey}
              defaultValue={content}
              onMarkdownUpdated={(markdown) => {
                setContent(markdown);
                setHasUnsavedChanges(true);
              }}
              onCrepeReady={(crepe) => {
                setEditor(crepe);
                crepe.setReadonly(mode === "view");
              }}
            />
          </div>
        </Box>
      </Box>
      </div>

      {/* Unsaved Changes Confirmation Dialog */}
      <Dialog
        open={showUnsavedDialog}
        onClose={handleUnsavedDialogClose}
        aria-labelledby="unsaved-dialog-title"
        aria-describedby="unsaved-dialog-description"
      >
        <DialogTitle id="unsaved-dialog-title">
          Unsaved Changes
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="unsaved-dialog-description">
            You have unsaved changes. Are you sure you want to {pendingAction === "close" ? "close" : "cancel editing"}? Your changes will be lost.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleUnsavedDialogClose}>
            Keep Editing
          </Button>
          <Button onClick={handleDiscardChanges} color="error" variant="contained">
            Discard Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Drawer>
  );
}

interface CrepeEditorProps {
  onMarkdownUpdated?: (markdown: string) => void;
  onCrepeReady?: (crepe: Crepe) => void;
  defaultValue?: string;
}

const CrepeEditor: React.FC<CrepeEditorProps> = ({
  onMarkdownUpdated,
  onCrepeReady,
  defaultValue = "",
}) => {
  const [crepe, setCrepe] = useState<Crepe | null>(null);

  useEditor((root) => {
    const crepe = new Crepe({
      features: {
        [Crepe.Feature.ImageBlock]: false,
      },
      defaultValue,
      root,
    });
    setCrepe(crepe);
    return crepe;
  });

  useEffect(() => {
    if (crepe) {
      crepe.on((listener) => {
        listener.markdownUpdated(() => {
          const markdown = crepe.getMarkdown();
          onMarkdownUpdated?.(markdown);
        });
      });
      onCrepeReady?.(crepe);
    }
  }, [crepe]);

  return <Milkdown />;
};

interface MilkdownEditorWrapperProps {
  onMarkdownUpdated?: (markdown: string) => void;
  onCrepeReady?: (crepe: Crepe) => void;
  defaultValue?: string;
}

export const MilkdownEditorWrapper: React.FC<MilkdownEditorWrapperProps> = ({
  onMarkdownUpdated,
  onCrepeReady,
  defaultValue,
}) => {
  return (
    <MilkdownProvider>
      <CrepeEditor
        onMarkdownUpdated={onMarkdownUpdated}
        onCrepeReady={onCrepeReady}
        defaultValue={defaultValue}
      />
    </MilkdownProvider>
  );
};
