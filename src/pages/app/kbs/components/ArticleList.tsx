import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
  Button,
  Tooltip,
} from "@mui/material";
import { FileText, MoreVertical, Edit2, Eye, Trash2, Folder, FolderPlus, Plus } from "lucide-react";
import {
  useKnowledgeBaseStore,
  KnowledgeBaseItem,
} from "../../../../stores/knowledgeBaseStore";
import { useNavigate, useParams, useSearchParams } from "react-router";
import CreateFolderDialog from "./CreateFolderDialog";
import CreateContentDialog from "./CreateContentDialog";
import DeleteConfirmationDialog from "./DeleteConfirmationDialog";

function getTypeIcon(type: "folder" | "article") {
  return type === "folder" ? <Folder size={16} /> : <FileText size={16} />;
}

function getTypeColor(source: string) {
  switch (source) {
    case "editor":
      return "primary";
    case "file":
      return "error";
    case "website":
      return "success";
    default:
      return "default";
  }
}

export default function ArticleList() {
  const {
    getItems,
    getItem,
    deleteItem,
    searchQuery,
    isLoading,
    isFolderLoading,
    loadFolderContents,
    isFolderLoaded,
    items, // Add items to watch for changes
    isDeleting,
  } = useKnowledgeBaseStore();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedArticle, setSelectedArticle] =
    React.useState<KnowledgeBaseItem | null>(null);
  const [currentFolder, setCurrentFolder] = React.useState<KnowledgeBaseItem | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  
  // Dialog states
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [isCreateContentOpen, setIsCreateContentOpen] = useState(false);

  const [, setSearchParams] = useSearchParams();
  const params = useParams();
  const { workspaceId } = useParams();
  const navigate = useNavigate();

  // Get current folder ID
  const currentFolderId = params.folderId ? parseInt(params.folderId) : null;
  
  // Get articles directly from store instead of local state
  const articles = React.useMemo(() => {
    return getItems(currentFolderId);
  }, [getItems, currentFolderId, items]); // items dependency ensures re-render on store changes

  const handleMenuClick = (
    event: React.MouseEvent<HTMLElement>,
    article: KnowledgeBaseItem,
  ) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedArticle(article);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedArticle(null);
  };

  const handleView = () => {
    if (selectedArticle) {
      setSearchParams({
        articleId: selectedArticle.id.toString(),
        mode: "view",
      });
    }
    handleMenuClose();
  };

  const handleEdit = () => {
    if (selectedArticle) {
      setSearchParams({
        articleId: selectedArticle.id.toString(),
        mode: "edit",
      });
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleConfirmDelete = async () => {
    if (selectedArticle) {
      try {
        await deleteItem(selectedArticle.id);
        setSearchParams({}); // Clear search params after deletion
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
    setDeleteDialogOpen(false);
  };

  useEffect(() => {
    const loadFolderData = async () => {
      const folderId = params.folderId ? parseInt(params.folderId) : null;
      
      // Get current folder info
      if (folderId) {
        const folder = getItem(folderId);
        setCurrentFolder(folder);
      } else {
        setCurrentFolder(null);
      }
      
      // Load folder contents if not already loaded
      if (!isFolderLoaded(folderId)) {
        await loadFolderContents(folderId);
      }
      
      // Articles are now computed directly from store, no need to set local state
    };

    loadFolderData();
  }, [params.folderId, getItem, loadFolderContents, isFolderLoaded]); // Simplified dependencies

  return (
    <Box sx={{ p: 3, height: "100%", overflow: "auto" }}>
      <Box sx={{ mb: 3, display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
        <Box>
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
            {currentFolder ? currentFolder.title : "All Articles"}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {articles.length} item{articles.length !== 1 ? "s" : ""}
            {searchQuery && ` matching "${searchQuery}"`}
          </Typography>
        </Box>
        
        {/* Action Buttons */}
        <Box sx={{ display: "flex", gap: 1 }}>
          <Tooltip title="Create Folder">
            <Button
              variant="outlined"
              size="small"
              startIcon={<FolderPlus size={16} />}
              onClick={() => setIsCreateFolderOpen(true)}
            >
              Add Folder
            </Button>
          </Tooltip>
          
          <Tooltip title="Add Content">
            <Button
              variant="contained"
              size="small"
              startIcon={<Plus size={16} />}
              onClick={() => setIsCreateContentOpen(true)}
            >
              Add Content
            </Button>
          </Tooltip>
        </Box>
      </Box>

      {articles.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: "center", bgcolor: "grey.50" }}>
          <FileText size={48} style={{ color: "#9e9e9e", marginBottom: 16 }} />
          <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
            {searchQuery ? "No items found" : "No items yet"}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchQuery
              ? "Try adjusting your search terms"
              : "Create your first folder or article to get started"}
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Updated</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading || isFolderLoading ? (
                // Skeleton loading rows
                [...Array(5)].map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Skeleton variant="text" width="60%" height={20} />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Skeleton variant="circular" width={20} height={20} />
                        <Skeleton variant="text" width={50} height={16} />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="rounded" width={80} height={24} />
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="text" width={80} height={16} />
                    </TableCell>
                    <TableCell align="right">
                      <Skeleton variant="circular" width={32} height={32} />
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                articles.map((article) => (
                  <TableRow
                    key={article.id}
                    hover
                    onClick={() => {
                      if (article.type === "folder") {
                        // Navigate to folder
                         navigate(`/app/w/${workspaceId}/knowledge/folder/${article.id}`);
                      } else {
                        // Open article in editor
                        setSearchParams({
                          articleId: article.id.toString(),
                          mode: "view",
                        });
                      }
                    }}
                    sx={{ cursor: "pointer" }}
                  >
                    <TableCell>{article.title}</TableCell>
                    <TableCell>
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Avatar
                          sx={{
                            width: 20,
                            height: 20,
                            bgcolor: `${getTypeColor(article.source)}.main`,
                          }}
                        >
                          {getTypeIcon(article.type)}
                        </Avatar>
                        <Typography
                          variant="caption"
                          sx={{ textTransform: "uppercase" }}
                        >
                          {article.type}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={article.status}
                        size="small"
                        color={
                          article.status === "published" 
                            ? "success" 
                            : article.status === "draft" 
                              ? "warning" 
                              : article.status === "archived"
                                ? "error"
                                : "default"
                        }
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(article.updated_at).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, article)}
                      >
                        <MoreVertical size={16} />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedArticle?.type === "article" && (
          <>
            <MenuItem onClick={handleView}>
              <ListItemIcon>
                <Eye size={16} />
              </ListItemIcon>
              <ListItemText>View</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleEdit}>
              <ListItemIcon>
                <Edit2 size={16} />
              </ListItemIcon>
              <ListItemText>Edit</ListItemText>
            </MenuItem>
          </>
        )}
        <MenuItem onClick={handleDelete} sx={{ color: "error.main" }}>
          <ListItemIcon>
            <Trash2 size={16} />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>
      
      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        itemType={selectedArticle?.type || 'article'}
        itemTitle={selectedArticle?.title || ''}
        isLoading={isDeleting}
      />
      
      {/* Create Folder Dialog */}
      <CreateFolderDialog
        open={isCreateFolderOpen}
        onClose={() => setIsCreateFolderOpen(false)}
        parentId={currentFolderId}
      />
      
      {/* Create Content Dialog */}
      <CreateContentDialog
        open={isCreateContentOpen}
        onClose={() => setIsCreateContentOpen(false)}
        parentId={currentFolderId}
      />
    </Box>
  );
}
