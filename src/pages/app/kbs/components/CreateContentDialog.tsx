import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Card,
  CardContent,
  CardActionArea,
  TextField,
  Input,
  InputLabel,
  FormControl
} from '@mui/material';
import { FileText, Upload, Globe, Plus } from 'lucide-react';
import { useKnowledgeBaseStore } from '../../../../stores/knowledgeBaseStore';
import { useSearchParams } from 'react-router';
import CreateWebsiteDialog from './CreateWebsiteDialog';

interface CreateContentDialogProps {
  open: boolean;
  onClose: () => void;
  parentId?: number | null;
}

type ContentType = 'editor' | 'file' | null;

export default function CreateContentDialog({ open, onClose, parentId = null }: CreateContentDialogProps) {
  const { addArticle } = useKnowledgeBaseStore();
  const [, setSearchParams] = useSearchParams();
  const [selectedType, setSelectedType] = useState<ContentType>(null);
  const [title, setTitle] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [showWebsiteDialog, setShowWebsiteDialog] = useState(false);

  const handleClose = () => {
    setSelectedType(null);
    setTitle('');
    setFile(null);
    setIsCreating(false);
    setShowWebsiteDialog(false);
    onClose();
  };

  const handleWebsiteDialogClose = () => {
    setShowWebsiteDialog(false);
  };

  const handleCreateFromEditor = async () => {
    setIsCreating(true);
    try {
      const articleId = await addArticle('Untitled', 'editor', parentId);
      // Open the article in edit mode
      setSearchParams({
        articleId: articleId.toString(),
        mode: 'edit',
      });
      handleClose();
    } catch (error) {
      setIsCreating(false);
    }
  };

  const handleImportFile = async () => {
    if (!file) return;
    
    setIsCreating(true);
    try {
      const articleTitle = title.trim() || file.name.replace(/\.[^/.]+$/, "");
      const articleId = await addArticle(articleTitle, 'file', parentId, file);
      // Open the article in view mode (since it's imported and read-only)
      setSearchParams({
        articleId: articleId.toString(),
        mode: 'view',
      });
      handleClose();
    } catch (error) {
      setIsCreating(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  const contentTypes = [
    {
      type: 'editor' as const,
      title: 'Write an Article',
      description: 'Create a new article using the markdown editor',
      icon: FileText,
      color: 'primary.main'
    },
    {
      type: 'file' as const,
      title: 'Import File',
      description: 'Upload a PDF or DOCX file',
      icon: Upload,
      color: 'secondary.main'
    },
    {
      type: 'website' as const,
      title: 'Import Website',
      description: 'Parse content from a website URL',
      icon: Globe,
      color: 'success.main'
    }
  ];

  const renderStepTwo = () => {
    switch (selectedType) {
      case 'file':
        return (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl fullWidth>
              <InputLabel shrink>Select File</InputLabel>
              <Input
                type="file"
                inputProps={{ accept: '.pdf,.docx,.doc' }}
                onChange={handleFileChange}
                sx={{ mt: 1 }}
              />
            </FormControl>
            {file && (
              <Typography variant="body2" color="text.secondary">
                Selected: {file.name}
              </Typography>
            )}
            <TextField
              fullWidth
              label="Title (Optional)"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Leave empty to use filename"
              variant="outlined"
            />
            <Typography variant="body2" color="text.secondary">
              Upload a PDF or Word document. The content will be extracted and imported as a read-only article.
            </Typography>
          </Box>
        );
      
      default:
        return null;
    }
  };

  const canProceed = () => {
    switch (selectedType) {
      case 'file':
        return file !== null;
      default:
        return false;
    }
  };

  const handleProceed = () => {
    switch (selectedType) {
      case 'file':
        handleImportFile();
        break;
    }
  };

  return (
    <>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Plus size={20} />
            {selectedType ? 'Add Content Details' : 'Add New Content'}
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {!selectedType ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                Choose the type of content you want to add:
              </Typography>
              
              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2 }}>
                {contentTypes.map((type) => {
                  const IconComponent = type.icon;
                  return (
                    <Card key={type.type} variant="outlined">
                      <CardActionArea
                        onClick={() => {
                          if (type.type === 'editor') {
                            // For editor, create directly without asking for title
                            handleCreateFromEditor();
                          } else if (type.type === 'website') {
                            // For website, open the website dialog
                            setShowWebsiteDialog(true);
                            onClose();
                          } else {
                            // For other types, go to step two
                            setSelectedType(type.type);
                          }
                        }}
                        sx={{ p: 2 }}
                      >
                        <CardContent sx={{ p: 0 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                            <Box
                              sx={{
                                p: 1,
                                borderRadius: 1,
                                bgcolor: type.color,
                                color: 'white',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              <IconComponent size={20} />
                            </Box>
                            <Typography variant="h6" component="div">
                              {type.title}
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            {type.description}
                          </Typography>
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  );
                })}
              </Box>
            </Box>
          ) : (
            renderStepTwo()
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} disabled={isCreating}>
            Cancel
          </Button>
          {selectedType && (
            <Button onClick={() => setSelectedType(null)} disabled={isCreating}>
              Back
            </Button>
          )}
          {selectedType && (
            <Button 
              onClick={handleProceed} 
              variant="contained"
              disabled={!canProceed() || isCreating}
            >
              {isCreating ? 'Creating...' : 'Create'}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      <CreateWebsiteDialog
        open={showWebsiteDialog}
        onClose={handleWebsiteDialogClose}
      />
    </>
  );
}
