
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box
} from '@mui/material';
import { AlertTriangle } from 'lucide-react';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemType: 'folder' | 'article';
  itemTitle: string;
  isLoading?: boolean;
}

export default function DeleteConfirmationDialog({
  open,
  onClose,
  onConfirm,
  itemType,
  itemTitle,
  isLoading = false
}: DeleteConfirmationDialogProps) {
  const isFolder = itemType === 'folder';
  
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AlertTriangle size={20} color="#f57c00" />
          Delete {isFolder ? 'Folder' : 'Article'}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Typography variant="body1" sx={{ mb: 2 }}>
          Are you sure you want to delete "{itemTitle}"?
        </Typography>
        
        {isFolder && (
          <Typography variant="body2" color="error" sx={{ fontWeight: 500 }}>
            ⚠️ This will permanently delete the folder and all its contents including subfolders and articles.
          </Typography>
        )}
        
        {!isFolder && (
          <Typography variant="body2" color="text.secondary">
            This action cannot be undone.
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button 
          onClick={onClose} 
          variant="outlined"
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button 
          onClick={onConfirm} 
          color="error" 
          variant="contained"
          disabled={isLoading}
        >
          {isLoading ? 'Deleting...' : 'Delete'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
