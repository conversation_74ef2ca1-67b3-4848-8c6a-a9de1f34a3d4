import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box
} from '@mui/material';
import { Folder } from 'lucide-react';
import { useKnowledgeBaseStore } from '../../../../stores/knowledgeBaseStore';

interface CreateFolderDialogProps {
  open: boolean;
  onClose: () => void;
  parentId?: number | null;
}

export default function CreateFolderDialog({ open, onClose, parentId = null }: CreateFolderDialogProps) {
  const { addItem } = useKnowledgeBaseStore();
  const [folderName, setFolderName] = useState('');

  const handleSubmit = async () => {
    if (folderName.trim()) {
      await addItem(folderName.trim(), "folder", parentId);
      setFolderName('');
      onClose();
    }
  };

  const handleCancel = () => {
    setFolderName('');
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleCancel} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Folder size={20} />
          Create New Folder
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
          <TextField
            autoFocus
            fullWidth
            label="Folder Name"
            value={folderName}
            onChange={(e) => setFolderName(e.target.value)}
            placeholder="Enter folder name..."
            variant="outlined"
          />
          
          <Typography variant="body2" color="text.secondary">
            {parentId ? 'Create a new subfolder in the selected folder.' : 'Create a new folder at the root level.'}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleCancel}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained"
          disabled={!folderName.trim()}
        >
          Create Folder
        </Button>
      </DialogActions>
    </Dialog>
  );
}
