import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { BrowserRouter, Route, Routes } from 'react-router'
import App from './App.tsx'
import './index.css'
import SignIn from './pages/auth/SignIn.tsx'
import SignUp from './pages/auth/SignUp.tsx'
import AuthLayout from './layouts/AuthLayout.tsx'
import DefaultLayout from './layouts/DefaultLayout.tsx'
import SimpleLayout from './layouts/SimpleLayout.tsx'
import SettingsLayout from './layouts/SettingsLayout.tsx'
import InboxLayout from './layouts/InboxLayout.tsx'

import WorkspacesPage from './pages/app/workspaces'
import InboxPage from './pages/app/inbox'
import KnowledgeBasePage from './pages/app/kbs/index.tsx'
import KnowledgeBaseLayout from './layouts/KnowledgeBaseLayout.tsx'
import KnowledgeBaseFolderPage from './pages/app/kbs/KnowledgeBaseFolderPage.tsx'
import WorkspaceSettingsPage from './pages/app/settings/WorkspaceSettingsPage.tsx'
import SettingsIndexPage from './pages/app/settings/SettingsIndexPage.tsx'
import TeamSettingsPage from './pages/app/settings/TeamSettingsPage.tsx'
import UsersPermissionsPage from './pages/app/settings/UsersPermissionsPage.tsx'
import RolesAccessPage from './pages/app/settings/RolesAccessPage.tsx'
import LabelsSegmentsPage from './pages/app/settings/LabelsSegmentsPage.tsx'
import ChannelsPage from './pages/app/settings/ChannelsPage.tsx'
import AppTheme from './AppTheme.tsx'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <AppTheme>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<App />} />
          <Route path="/auth" element={<AuthLayout />}>
            <Route index element={<SignIn />} />
            <Route path="/auth/sign-in" element={<SignIn />} />
            <Route path="/auth/sign-up" element={<SignUp />} />
          </Route>
          <Route path="/app" element={<SimpleLayout />}>
            <Route index element={<WorkspacesPage />} />
          </Route>
          <Route path="/app/w/:workspaceId" element={<DefaultLayout />}>
            <Route index element={<WorkspacesPage />} />
            <Route path="/app/w/:workspaceId/inbox" element={<InboxLayout />}>
              <Route index element={<InboxPage />} />
            </Route>
            <Route path="/app/w/:workspaceId/knowledge" element={<KnowledgeBaseLayout />} >
              <Route index element={<KnowledgeBasePage />} />
              <Route path="/app/w/:workspaceId/knowledge/folder/:folderId" element={<KnowledgeBaseFolderPage />} />
            </Route>
            <Route path="/app/w/:workspaceId/settings" element={<SettingsLayout />}>
              <Route index element={<SettingsIndexPage />} />
              <Route path="/app/w/:workspaceId/settings/workspace" element={<WorkspaceSettingsPage />} />
              <Route path="/app/w/:workspaceId/settings/users" element={<UsersPermissionsPage />} />
              <Route path="/app/w/:workspaceId/settings/roles" element={<RolesAccessPage />} />
              <Route path="/app/w/:workspaceId/settings/teams" element={<TeamSettingsPage />} />
              <Route path="/app/w/:workspaceId/settings/labels" element={<LabelsSegmentsPage />} />
              <Route path="/app/w/:workspaceId/settings/channels" element={<ChannelsPage />} />
            </Route>
          </Route>
        </Routes>
      </BrowserRouter>
    </AppTheme>
  </StrictMode>,
)
